"""
Reasoning Assistant with Dhanishta 2.0
A powerful assistant that leverages intermediate thinking for problem-solving.
"""

import os
import re
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from HelpingAI import HAI, HAIError


@dataclass
class ReasoningResult:
    """Result of a reasoning operation"""
    problem: str
    solution: str
    thinking_process: Optional[str] = None
    confidence: Optional[float] = None
    domain: Optional[str] = None
    steps: Optional[List[str]] = None


class ReasoningAssistant:
    """
    A reasoning assistant powered by Dhanishta 2.0's intermediate thinking capabilities.
    """
    
    def __init__(
        self,
        model: str = "Dhanishtha-2.0-preview",
        temperature: float = 0.7,
        max_tokens: int = 2048,
        show_thinking_by_default: bool = True
    ):
        """
        Initialize the reasoning assistant.
        
        Args:
            model: The model to use (default: Dhanishtha-2.0-preview)
            temperature: Sampling temperature (0.0-1.0)
            max_tokens: Maximum tokens in response
            show_thinking_by_default: Whether to show thinking by default
        """
        self.hai = HAI()
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.show_thinking_by_default = show_thinking_by_default
        
        # Reasoning style templates
        self.reasoning_styles = {
            "step_by_step": "Please solve this step by step, showing your work clearly.",
            "socratic_method": "Please approach this using the Socratic method, asking and answering key questions.",
            "scientific_method": "Please use the scientific method: observe, hypothesize, test, and conclude.",
            "creative": "Please approach this creatively, considering multiple perspectives and innovative solutions."
        }
        
        self.current_style = "step_by_step"
    
    def solve_problem(
        self,
        problem: str,
        show_thinking: Optional[bool] = None,
        domain: Optional[str] = None,
        style: Optional[str] = None
    ) -> ReasoningResult:
        """
        Solve a general problem with reasoning.
        
        Args:
            problem: The problem to solve
            show_thinking: Whether to show the thinking process
            domain: Problem domain (math, logic, science, etc.)
            style: Reasoning style to use
            
        Returns:
            ReasoningResult with solution and thinking process
        """
        if show_thinking is None:
            show_thinking = self.show_thinking_by_default
        
        # Prepare the prompt
        prompt = self._prepare_prompt(problem, domain, style)
        
        try:
            response = self.hai.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                hide_think=not show_thinking
            )
            
            content = response.choices[0].message.content
            
            # Extract thinking process if present
            thinking_process = None
            solution = content
            
            if show_thinking and "<think>" in content:
                thinking_parts = re.findall(r'<think>(.*?)</think>', content, re.DOTALL)
                if thinking_parts:
                    thinking_process = "\n".join(thinking_parts)
                    # Remove thinking blocks from solution
                    solution = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL).strip()
            
            # Extract steps if present
            steps = self._extract_steps(solution)
            
            return ReasoningResult(
                problem=problem,
                solution=solution,
                thinking_process=thinking_process,
                domain=domain,
                steps=steps
            )
            
        except HAIError as e:
            return ReasoningResult(
                problem=problem,
                solution=f"Error: {str(e)}",
                thinking_process=None,
                domain=domain
            )
    
    def solve_math_problem(self, problem: str) -> ReasoningResult:
        """Solve a mathematical problem with specialized prompting."""
        enhanced_prompt = f"""
        Solve this mathematical problem step by step. Show all calculations clearly.
        
        Problem: {problem}
        
        Please:
        1. Identify what is being asked
        2. List the given information
        3. Show each calculation step
        4. Verify your answer
        """
        
        return self.solve_problem(enhanced_prompt, domain="mathematics")
    
    def solve_logic_puzzle(self, problem: str) -> ReasoningResult:
        """Solve logic puzzles and riddles."""
        enhanced_prompt = f"""
        Solve this logic puzzle systematically.
        
        Problem: {problem}
        
        Please:
        1. List all given constraints
        2. Work through the logic step by step
        3. Eliminate impossible options
        4. Arrive at the solution
        """
        
        return self.solve_problem(enhanced_prompt, domain="logic")
    
    def explain_concept(self, concept: str, level: str = "intermediate") -> ReasoningResult:
        """Explain a concept with reasoning."""
        enhanced_prompt = f"""
        Explain the concept of "{concept}" at a {level} level.
        
        Please:
        1. Define the concept clearly
        2. Explain the underlying principles
        3. Provide examples
        4. Connect to related concepts
        """
        
        return self.solve_problem(enhanced_prompt, domain="education")
    
    def solve_coding_problem(self, problem: str, language: str = "Python") -> ReasoningResult:
        """Solve programming problems with reasoning."""
        enhanced_prompt = f"""
        Solve this programming problem in {language}.
        
        Problem: {problem}
        
        Please:
        1. Understand the requirements
        2. Plan the algorithm
        3. Write the code with comments
        4. Explain the solution approach
        """
        
        return self.solve_problem(enhanced_prompt, domain="programming")
    
    def verify_solution(self, problem: str, solution: str) -> ReasoningResult:
        """Verify if a solution is correct."""
        verification_prompt = f"""
        Verify if this solution is correct for the given problem.
        
        Problem: {problem}
        
        Proposed Solution: {solution}
        
        Please:
        1. Check the logic step by step
        2. Verify calculations if applicable
        3. Identify any errors
        4. Provide the correct solution if needed
        """
        
        return self.solve_problem(verification_prompt, domain="verification")
    
    def collaborative_solve(
        self,
        problem: str,
        perspectives: List[str] = None
    ) -> Dict[str, ReasoningResult]:
        """Solve a problem from multiple perspectives."""
        if perspectives is None:
            perspectives = ["analytical", "creative", "practical"]
        
        results = {}
        
        for perspective in perspectives:
            prompt = f"""
            Solve this problem from a {perspective} perspective.
            
            Problem: {problem}
            
            Focus on {perspective} approaches and insights.
            """
            
            results[perspective] = self.solve_problem(prompt, domain=perspective)
        
        return results
    
    def set_reasoning_style(self, style: str):
        """Set the reasoning style for problem solving."""
        if style in self.reasoning_styles:
            self.current_style = style
        else:
            raise ValueError(f"Unknown style: {style}. Available: {list(self.reasoning_styles.keys())}")
    
    def _prepare_prompt(self, problem: str, domain: Optional[str], style: Optional[str]) -> str:
        """Prepare the prompt with appropriate context."""
        prompt_parts = []
        
        # Add reasoning style
        if style and style in self.reasoning_styles:
            prompt_parts.append(self.reasoning_styles[style])
        elif self.current_style:
            prompt_parts.append(self.reasoning_styles[self.current_style])
        
        # Add domain-specific context
        if domain:
            domain_contexts = {
                "mathematics": "Focus on mathematical accuracy and clear calculations.",
                "logic": "Use logical reasoning and systematic elimination.",
                "science": "Apply scientific principles and evidence-based reasoning.",
                "programming": "Consider algorithm efficiency and code clarity.",
                "education": "Make explanations clear and pedagogically sound."
            }
            if domain in domain_contexts:
                prompt_parts.append(domain_contexts[domain])
        
        # Add the main problem
        prompt_parts.append(f"Problem: {problem}")
        
        return "\n\n".join(prompt_parts)
    
    def _extract_steps(self, solution: str) -> Optional[List[str]]:
        """Extract numbered steps from the solution."""
        # Look for numbered lists
        step_patterns = [
            r'(\d+\.\s+[^\n]+)',  # 1. Step one
            r'(Step \d+:?\s+[^\n]+)',  # Step 1: Description
            r'(\d+\)\s+[^\n]+)'  # 1) Step one
        ]
        
        for pattern in step_patterns:
            steps = re.findall(pattern, solution, re.MULTILINE)
            if steps:
                return [step.strip() for step in steps]
        
        return None


# Example usage and testing
if __name__ == "__main__":
    # Initialize the assistant
    assistant = ReasoningAssistant()
    
    # Test problems
    test_problems = [
        "How many letter 'r' are in the word 'strawberry'?",
        "If it takes 5 machines 5 minutes to make 5 widgets, how long would it take 100 machines to make 100 widgets?",
        "Three friends have red, blue, and green hats. Alice doesn't have red, Bob doesn't have blue, Charlie doesn't have green. Who has which color?"
    ]
    
    print("🧠 Reasoning Assistant Demo")
    print("=" * 50)
    
    for i, problem in enumerate(test_problems, 1):
        print(f"\n📝 Problem {i}: {problem}")
        print("-" * 40)
        
        result = assistant.solve_problem(problem, show_thinking=True)
        
        if result.thinking_process:
            print("🤔 Thinking Process:")
            print(result.thinking_process)
            print()
        
        print("💡 Solution:")
        print(result.solution)
        print("\n" + "=" * 50)
