import os
from HelpingAI import HAI
import re

# Initialize the client
hai = HAI()

def demonstrate_self_correction(prompt, temperature=0.3):
    """Helper function to show self-correction in action"""
    print(f"🤔 Problem: {prompt}")
    print("=" * 70)
    
    response = hai.chat.completions.create(
        model="Dhanishtha-2.0-preview",
        messages=[{"role": "user", "content": prompt}],
        hide_think=False,
        temperature=temperature,
        max_tokens=2500
    )
    
    content = response.choices[0].message.content
    
    # Highlight self-correction patterns
    correction_patterns = [
        r"(wait[,.]?\s+let me.*?check|actually[,.]?\s+|hold on[,.]?\s+|let me reconsider|that's not right|I made an error)",
        r"(let me double.?check|let me verify|wait[,.]?\s+that's wrong|actually[,.]?\s+that's incorrect)"
    ]
    
    corrections_found = []
    for pattern in correction_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        corrections_found.extend(matches)
    
    print(content)
    
    if corrections_found:
        print("\n🔍 SELF-CORRECTION DETECTED:")
        print("-" * 40)
        for correction in corrections_found:
            print(f"• {correction}")
    
    print("\n" + "=" * 70 + "\n")
    return response

print("🔄 Ready to explore self-correction capabilities!")

# Calculation that might trigger self-correction
demonstrate_self_correction(
    "Calculate 17 × 23 step by step. Then verify your answer using a different method."
)

# Percentage calculation with verification
demonstrate_self_correction(
    "What is 18% of 250? Show your calculation and then double-check it."
)

# Complex fraction problem
demonstrate_self_correction(
    "Simplify: (3/4 + 2/3) ÷ (5/6 - 1/2). Show each step and verify the final answer."
)

# Logic puzzle with potential for error
demonstrate_self_correction(
    "In a group of 30 people, 18 like coffee, 15 like tea, and 8 like both. "
    "How many people like neither coffee nor tea? "
    "Solve this step by step and check your reasoning."
)

# Probability problem
demonstrate_self_correction(
    "You roll two dice. What's the probability of getting a sum of 7 or 11? "
    "List all possible outcomes and verify your calculation."
)

# Physics problem with common misconceptions
demonstrate_self_correction(
    "A 10 kg object is dropped from a height of 20 meters. "
    "How long does it take to hit the ground? (Ignore air resistance) "
    "Use g = 9.8 m/s² and show your work. Then verify using energy conservation."
)

# Chemistry stoichiometry
demonstrate_self_correction(
    "How many grams of CO₂ are produced when 25 grams of C₂H₆ burns completely in oxygen? "
    "Write the balanced equation, show your calculation, and verify using molar ratios."
)

# Statistical analysis with potential pitfalls
demonstrate_self_correction(
    "A survey of 100 people shows: 60 prefer brand A, 45 prefer brand B, 25 prefer both. "
    "What percentage prefer only brand A? Only brand B? Neither? "
    "Create a Venn diagram analysis and verify your percentages add up correctly."
)

# Average calculation with verification
demonstrate_self_correction(
    "Test scores: 85, 92, 78, 96, 88, 91, 83, 89, 94, 87. "
    "Calculate the mean, median, and mode. Double-check each calculation."
)

# Complex word problem
demonstrate_self_correction(
    "Sarah has twice as many apples as oranges. She has 3 more oranges than bananas. "
    "If she has 15 bananas, how many pieces of fruit does she have in total? "
    "Set up equations and verify your answer makes sense."
)

# Time and distance problem
demonstrate_self_correction(
    "Two trains start from stations 300 km apart and travel toward each other. "
    "Train A travels at 80 km/h, Train B at 70 km/h. "
    "When do they meet and how far has each train traveled? "
    "Solve and then verify by checking that distances add up to 300 km."
)

def analyze_correction_types():
    """Analyze different types of self-correction patterns"""
    
    correction_examples = {
        "Calculation Check": "What's 15% of 80? Show your work and verify.",
        "Logic Verification": "If all birds can fly, and penguins are birds, can penguins fly? Explain your reasoning.",
        "Unit Conversion": "Convert 2.5 hours to minutes and seconds. Double-check your conversion.",
        "Reading Comprehension": "A recipe calls for 2/3 cup flour for 12 cookies. How much flour for 18 cookies?"
    }
    
    correction_indicators = {
        "wait": 0, "actually": 0, "let me check": 0, "that's wrong": 0,
        "reconsider": 0, "verify": 0, "double-check": 0, "mistake": 0
    }
    
    for correction_type, problem in correction_examples.items():
        print(f"\n🔍 Testing {correction_type}:")
        print("-" * 40)
        
        response = hai.chat.completions.create(
            model="Dhanishtha-2.0-preview",
            messages=[{"role": "user", "content": problem}],
            hide_think=False,
            temperature=0.3,
            max_tokens=1500
        )
        
        content = response.choices[0].message.content.lower()
        
        # Count correction indicators
        found_corrections = []
        for indicator in correction_indicators:
            count = content.count(indicator)
            if count > 0:
                correction_indicators[indicator] += count
                found_corrections.append(f"{indicator}: {count}")
        
        if found_corrections:
            print(f"Corrections found: {', '.join(found_corrections)}")
        else:
            print("No explicit corrections detected")
        
        # Show thinking snippet
        if "<think>" in response.choices[0].message.content:
            thinking = response.choices[0].message.content.split("<think>")[1].split("</think>")[0]
            print(f"Thinking preview: {thinking[:150]}...")
    
    print("\n📊 OVERALL CORRECTION PATTERNS:")
    print("=" * 40)
    for indicator, count in correction_indicators.items():
        if count > 0:
            print(f"{indicator.title()}: {count} occurrences")

analyze_correction_types()

# Creative writing with self-improvement
demonstrate_self_correction(
    "Write a short poem about artificial intelligence. "
    "After writing it, review and suggest improvements to make it more engaging.",
    temperature=0.7
)

# Meta-reasoning about self-correction
demonstrate_self_correction(
    "Explain how you identify and correct your own mistakes during reasoning. "
    "What triggers you to double-check your work? "
    "Provide examples of common error patterns you watch for."
)