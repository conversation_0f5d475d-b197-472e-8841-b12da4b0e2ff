import os
from HelpingAI import HAI
import json

# Initialize the client
hai = HAI()

print("🧠 Ready to explore Dhanishta 2.0's thinking process!")

def show_thinking(prompt, temperature=0.7):
    """Helper function to display thinking process clearly"""
    print(f"🤔 Question: {prompt}")
    print("=" * 60)
    
    response = hai.chat.completions.create(
        model="Dhanishtha-2.0-preview",
        messages=[{"role": "user", "content": prompt}],
        hide_think=False,
        temperature=temperature,
        max_tokens=2048
    )
    
    print(response.choices[0].message.content)
    print("\n" + "=" * 60 + "\n")

# Simple counting example
show_thinking("How many letter 'r' are in the word 'strawberry'?")

# Mathematical reasoning example
show_thinking(
    "If it takes 5 machines 5 minutes to make 5 widgets, how long would it take 100 machines to make 100 widgets?",
    temperature=0.3  # Lower temperature for logical reasoning
)

# Percentage calculation
show_thinking("What's 15% of 240? Show your work.", temperature=0.3)

# Logic puzzle
show_thinking(
    "Three friends <PERSON>, <PERSON>, and <PERSON> have different colored hats: red, blue, and green. "
    "<PERSON> doesn't have red, <PERSON> doesn't have blue, and <PERSON> doesn't have green. "
    "Who has which color hat?" 
)

# Classic riddle
show_thinking(
    "A man lives on the 20th floor of an apartment building. Every morning he takes the elevator down to the ground floor. "
    "When he comes home, he takes the elevator to the 10th floor and walks the rest of the way... except on rainy days, "
    "when he takes the elevator all the way to the 20th floor. Why?",
    temperature=0.1
)

# Example that might trigger self-correction
show_thinking(
    "If a train leaves Station A at 2:00 PM traveling at 60 mph, and another train leaves Station B "
    "at 3:00 PM traveling at 80 mph toward Station A, and the stations are 280 miles apart, "
    "at what time will they meet?", 
    temperature=0.1
)

# Spanish thinking
show_thinking("¿Cuántas letras 'a' hay en la palabra 'banana'? Muestra tu proceso de pensamiento.")

# French thinking
show_thinking("Si un rectangle a une longueur de 12 cm et une largeur de 8 cm, quelle est son aire?")

# Creative writing with reasoning
show_thinking(
    "Write a short story about a robot who discovers emotions. "
    "Think about the plot structure, character development, and themes before writing.",
    temperature=0.8  # Higher temperature for creativity
)

# Scientific explanation
show_thinking(
    "Explain why the sky is blue. Break down the scientific explanation step by step.",
    temperature=0.4
)

question = "How would you solve world hunger?"

print("🧠 WITH THINKING PROCESS:")
print("=" * 50)
response_with_thinking = hai.chat.completions.create(
    model="Dhanishtha-2.0-preview",
    messages=[{"role": "user", "content": question}],
    hide_think=False,
    max_tokens=1500
)
print(response_with_thinking.choices[0].message.content)

print("\n\n🎯 WITHOUT THINKING PROCESS (CLEAN OUTPUT):")
print("=" * 50)
response_without_thinking = hai.chat.completions.create(
    model="Dhanishtha-2.0-preview",
    messages=[{"role": "user", "content": question}],
    hide_think=True,
    max_tokens=1500
)
print(response_without_thinking.choices[0].message.content)

def compare_temperatures(prompt):
    """Compare thinking at different temperature settings"""
    temperatures = [0.3, 0.7, 1.0]
    
    for temp in temperatures:
        print(f"🌡️ TEMPERATURE: {temp}")
        print("-" * 30)
        
        response = hai.chat.completions.create(
            model="Dhanishtha-2.0-preview",
            messages=[{"role": "user", "content": prompt}],
            hide_think=False,
            temperature=temp,
            max_tokens=1000
        )
        
        # Show just the thinking part
        content = response.choices[0].message.content
        if "<think>" in content:
            thinking_part = content.split("<think>")[1].split("</think>")[0]
            print(f"Thinking: {thinking_part[:200]}...")
        print("\n")

# Compare thinking styles at different temperatures
compare_temperatures("What's the best way to learn a new programming language?")

def analyze_thinking(prompt):
    """Analyze the structure of the thinking process"""
    response = hai.chat.completions.create(
        model="Dhanishtha-2.0-preview",
        messages=[{"role": "user", "content": prompt}],
        hide_think=False,
        max_tokens=2048
    )
    
    content = response.choices[0].message.content
    
    # Count thinking blocks
    think_count = content.count("<think>")
    ser_count = content.count("<ser>")  # Structured Emotional Reasoning
    
    # Extract thinking content
    thinking_blocks = []
    remaining = content
    while "<think>" in remaining:
        start = remaining.find("<think>") + 7
        end = remaining.find("</think>")
        if end != -1:
            thinking_blocks.append(remaining[start:end].strip())
            remaining = remaining[end + 8:]
        else:
            break
    
    print(f"📊 THINKING ANALYSIS")
    print("=" * 40)
    print(f"Number of thinking blocks: {think_count}")
    print(f"Number of emotional reasoning blocks: {ser_count}")
    print(f"Total response length: {len(content)} characters")
    
    if thinking_blocks:
        thinking_length = sum(len(block) for block in thinking_blocks)
        print(f"Thinking content length: {thinking_length} characters")
        print(f"Thinking ratio: {thinking_length/len(content)*100:.1f}%")
        
        print("\n🧠 THINKING BLOCKS:")
        for i, block in enumerate(thinking_blocks, 1):
            print(f"\nBlock {i}: {block[:100]}..." if len(block) > 100 else f"\nBlock {i}: {block}")
    
    print("\n💬 FULL RESPONSE:")
    print("-" * 40)
    print(content)

# Analyze a complex reasoning task
analyze_thinking(
    "Design a sustainable city of the future. Consider environmental, social, and economic factors."
)