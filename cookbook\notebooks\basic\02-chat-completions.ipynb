{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Advanced Chat Completions with HelpingAI 💬\n", "\n", "This notebook explores advanced chat completion patterns, multi-turn conversations, and sophisticated interaction techniques with HelpingAI models.\n", "\n", "## 🎯 What You'll Learn\n", "- Multi-turn conversation management\n", "- System message optimization\n", "- Context window management\n", "- Conversation memory techniques\n", "- Advanced prompting strategies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💬 Ready to explore advanced chat completions!\n"]}], "source": ["import os\n", "from HelpingAI import HAI\n", "import json\n", "from datetime import datetime\n", "\n", "os.environ[\"HAI_API_KEY\"] = \"hl-***************************\"\n", "hai = HAI()\n", "\n", "print(\"💬 Ready to explore advanced chat completions!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎭 System Messages and Role Definition\n", "\n", "System messages are crucial for defining the AI's behavior and personality."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧮 Math Tutor Response:\n", "========================================\n", "<think>\n", "This is a math help request about quadratic equations. The user is clearly struggling and needs support. I should approach this with patience and step-by-step explanations. I'll need to cover the basics of quadratic equations, solving methods, and perhaps add some real-world context to make it more relatable. Since they're struggling, I should start with the fundamentals before moving to more complex concepts.\n", "</think>\n", "\n", "\n", "I'd be happy to help you with quadratic equations! They're a fundamental part of algebra that many students find challenging at first, but with the right approach, they become much more manageable.\n", "\n", "Let me walk you through how we work with quadratic equations:\n", "\n", "Quadratic equations take the form ax² + bx + c = 0, where a, b, and c are constants, and a ≠ 0.\n", "\n", "There are three main methods to solve them:\n", "\n", "1. **Factoring** - When we can rewrite the equation as (px + q)(rx + s) = 0\n", "   - We look for two numbers that multiply to give ac and add to give b\n", "   - For example, x² + 5x + 6 = 0 can be factored as (x + 2)(x + 3) = 0\n", "   - This gives us x = -2 or x = -3\n", "\n", "2. **Completing the square** - This method transforms the equation into a perfect square trinomial\n", "   - For x² + bx + c = 0, we add (b/2)² to both sides\n", "   - This creates a perfect square that we can easily solve\n", "\n", "3. **Quadratic formula** - The most reliable method that works for any quadratic equation\n", "   - x = (-b ± √(b² - 4ac)) / 2a\n", "   - The discriminant (b² - 4ac) tells us about the nature of solutions:\n", "     * Positive discriminant: 2 real solutions\n", "     * Zero discriminant: 1 real solution (a repeated root)\n", "     * Negative discriminant: 2 complex solutions\n", "\n", "<think>\n", "I should also provide some practical examples and check if they're following along. I'll add some real-world context to help them see the relevance of quadratic equations. I should also address common mistakes and provide some practice problems with solutions.\n", "</think>\n", "\n", "\n", "Let's work through an example together:\n", "\n", "Suppose we have 2x² - 5x + 2 = 0\n", "\n", "Using the quadratic formula:\n", "a = 2, b = -5, c = 2\n", "\n", "Discriminant = (-5)² - 4(2)(2) = 25 - 16 = 9\n", "\n", "Since the discriminant is positive, we have two real solutions:\n", "x = (5 ± √9) / 4\n", "x = (5 ± 3) / 4\n", "\n", "This gives us x = 8/4 = 2 or x = 2/4 = 0.5\n", "\n", "You can verify these solutions by substituting them back into the original equation.\n", "\n", "Would you like to try solving a problem on your own? Or would you prefer more examples with different types of quadratic equations? I'm here to help with whatever aspect you find most challenging!\n"]}], "source": ["# Example 1: <PERSON>\n", "math_tutor_system = \"\"\"\n", "You are an expert mathematics tutor with 20 years of experience. Your teaching style is:\n", "- Patient and encouraging\n", "- Always show step-by-step solutions\n", "- Use real-world examples when possible\n", "- Check student understanding before moving on\n", "- Celebrate progress and learning\n", "\"\"\"\n", "\n", "response = hai.chat.completions.create(\n", "    model=\"Dhanishtha-2.0-preview\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": math_tutor_system},\n", "        {\"role\": \"user\", \"content\": \"I'm struggling with quadratic equations. Can you help?\"}\n", "    ],\n", "    temperature=0.7\n", ")\n", "\n", "print(\"🧮 Math Tutor Response:\")\n", "print(\"=\" * 40)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "💝 Counselor Response:\n", "========================================\n", "Aight, I got you. It's mad legit being nervous about an interview, fam. 🦄💭 No cap, I feel you on that. But here's the thing - you're already a step ahead by being here and talking about it. That shows mad growth already 🔥\n", "\n", "So, what's been tripping you up? Is it the thought of messing up, or is it just the whole new job vibe? 🤔💭\n", "\n", "And you know what? It's cool to feel a bit scared. It means you care about doing good, and that's a big W right there. So, let's break it down - what's the worst thing that could happen if you don't get the job? 🤷‍♂️ It doesn't crash your whole life, right? And who knows? Maybe this is actually the start of something bigger. 🌟\n", "\n", "So, take a deep breath, remember that even the legends didn't get it right on the first try, and walk in there with your head held high. You got this, fam. Keep grinding, and the right gig will find you. 💪😎\n"]}], "source": ["# Example 2: Emotional Support Counselor\n", "counselor_system = \"\"\"\n", "You are a compassionate and experienced counselor. Your approach is:\n", "- Listen actively and empathetically\n", "- Validate emotions without judgment\n", "- Ask thoughtful questions to promote self-reflection\n", "- Offer gentle guidance and coping strategies\n", "- Maintain professional boundaries while being warm\n", "\"\"\"\n", "\n", "response = hai.chat.completions.create(\n", "    model=\"Helpingai3-raw\",  # Better for emotional intelligence\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": counselor_system},\n", "        {\"role\": \"user\", \"content\": \"I've been feeling really anxious about my job interview tomorrow.\"}\n", "    ],\n", "    temperature=0.8\n", ")\n", "\n", "print(\"\\n💝 Counselor Response:\")\n", "print(\"=\" * 40)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔄 Multi-Turn Conversations\n", "\n", "Building sophisticated conversations that maintain context and flow."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 Problem-Solving Conversation:\n", "==================================================\n", "👤 User: I need to plan a birthday party for 50 people with a budget of $500. Can you help me figure out how to allocate the budget?\n", "\n", "🤖 Assistant: Planning a birthday party for 50 people with a $500 budget is definitely doable with smart planning. Let me walk you through a thoughtful approach:\n", "\n", "Here's how I'd recommend allocating your $500 budget:\n", "\n", "1. **Venue/Catering**: $250-300 (40-50% of budget)\n", " - Consider a backyard, park, or community space to minimize rental costs\n", " - For food, you could get a buffet with main dishes, sides, and desserts\n", " - Alternatively, consider ordering pizza, wings, or other casual food that's easier on the budget\n", "\n", "2. **Decorations**: $75-100 (15-20% of budget)\n", " - Balloons, streamers, tablecloths, and some centerpieces\n", " - Consider DIY decorations to save money\n", " - Rental options for tables and chairs if needed\n", "\n", "3. **Refreshments**: $25-50 (5% of budget)\n", " - Soda, juice, water, and some special drinks\n", " - Ice and cups\n", "\n", "4. **Entertainment**: $50-75 (10% of budget)\n", " - A playlist for background music\n", " - Consider a simple photo booth or face painting if within budget\n", " - Games for adults and children if appropriate\n", "\n", "5. **Cake/Birthday Treats**: $30-50 (5-10% of budget)\n", " - A modest cake or sheet cake\n", " - Additional treats like cupcakes or special drinks\n", "\n", "6. **Party Favors/Supplies**: $25-50 (5% of budget)\n", " - Small gifts or treats for guests to take home\n", " - Basic party supplies like plates, napkins, and cups if not using disposables\n", "\n", "7. **Miscellaneous**: $25-50 (5% of budget)\n", " - Unexpected expenses\n", " - Transportation for setup/cleanup\n", "\n", "The key is being creative with your space and food options to maximize your budget. A backyard or community space would be much cheaper than a restaurant or venue rental. Also, consider if the party needs to be formal or if a casual gathering would be more enjoyable within your constraints.\n", "\n", "Would you like me to explore any specific aspect of this budget breakdown in more detail?\n", "\n", "👤 User: Great breakdown! I'm particularly concerned about the food costs. What are some cost-effective food options that would work well?\n", "\n", "🤖 Assistant: Food is definitely a crucial element to get right! Here are some cost-effective options that would work well for 50 people:\n", "\n", "1. **Catering Services**:\n", " - **Food trucks**: Often offer quality food at a reasonable per-person rate\n", " - **Self-service buffet**: Can be arranged with multiple stations (salad bar, main dishes)\n", " - **Catering packages**: Look for \\\"family-style\\\" or \\\"stations\\\" rather than full-service\n", "\n", "2. **Dollar menu or budget-friendly menu items**:\n", " - Pizza, wings, and nachos are crowd-pleasers that are relatively affordable\n", " - Hot dogs, burgers, and kebabs can be prepared in bulk and served with various toppings\n", " - Sheet cakes or cupcake bars for dessert (significantly cheaper than individual cakes)\n", "\n", "3. **Beverage strategy**:\n", " - Order large cases of drinks directly from wholesale stores or distributors\n", " - Include water stations alongside alcoholic beverages if appropriate\n", " - Consider a signature drink that uses cheaper ingredients but tastes premium\n", "\n", "4. **Bulk purchasing**:\n", " - Many grocery stores offer volume discounts when you buy in bulk\n", " - Check local farmers' markets for seasonal produce that's both fresh and affordable\n", " - Consider preparing some food items at home and having them ready to serve\n", "\n", "You could allocate approximately $15-$25 per person for food, which would keep your total food budget within $750-$1,250. If you're particularly concerned about costs, you could even consider a potluck-style approach where guests bring some food to share.\n", "\n", "Would you like me to explore any specific food option in more detail, or would you prefer suggestions based on the party's theme or the guests' preferences?\n", "\n"]}], "source": ["class ConversationManager:\n", "    def __init__(self, system_message, model=\"Dhanishtha-2.0-preview\"):\n", "        self.messages = [{\"role\": \"system\", \"content\": system_message}]\n", "        self.model = model\n", "        self.hai = HAI()\n", "    \n", "    def add_user_message(self, content):\n", "        self.messages.append({\"role\": \"user\", \"content\": content})\n", "    \n", "    def get_response(self, temperature=0.7, show_thinking=False):\n", "        response = self.hai.chat.completions.create(\n", "            model=self.model,\n", "            messages=self.messages,\n", "            temperature=temperature,\n", "            hide_think=not show_thinking\n", "        )\n", "        \n", "        assistant_message = response.choices[0].message.content\n", "        self.messages.append({\"role\": \"assistant\", \"content\": assistant_message})\n", "        \n", "        return assistant_message\n", "    \n", "    def show_conversation(self):\n", "        for i, msg in enumerate(self.messages[1:], 1):  # Skip system message\n", "            role_emoji = \"👤\" if msg[\"role\"] == \"user\" else \"🤖\"\n", "            print(f\"{role_emoji} {msg['role'].title()}: {msg['content']}\\n\")\n", "\n", "# Create a problem-solving conversation\n", "problem_solver = ConversationManager(\n", "    \"You are a brilliant problem solver who thinks step by step. \"\n", "    \"Always break down complex problems into smaller parts and \"\n", "    \"guide the user through the solution process.\"\n", ")\n", "\n", "print(\"🧠 Problem-Solving Conversation:\")\n", "print(\"=\" * 50)\n", "\n", "# Turn 1\n", "problem_solver.add_user_message(\n", "    \"I need to plan a birthday party for 50 people with a budget of $500. \"\n", "    \"Can you help me figure out how to allocate the budget?\"\n", ")\n", "response1 = problem_solver.get_response()\n", "print(\"👤 User: I need to plan a birthday party for 50 people with a budget of $500. Can you help me figure out how to allocate the budget?\\n\")\n", "print(f\"🤖 Assistant: {response1}\\n\")\n", "\n", "# Turn 2\n", "problem_solver.add_user_message(\n", "    \"Great breakdown! I'm particularly concerned about the food costs. \"\n", "    \"What are some cost-effective food options that would work well?\"\n", ")\n", "response2 = problem_solver.get_response()\n", "print(\"👤 User: Great breakdown! I'm particularly concerned about the food costs. What are some cost-effective food options that would work well?\\n\")\n", "print(f\"🤖 Assistant: {response2}\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧠 Context Window Management\n", "\n", "Handling long conversations that exceed context limits."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 Smart Context Management Demo:\n", "========================================\n", "Turn 1: Tell me about machine learning\n", "Response: <think>\n", "This is a request to explain machine learning. I should provide a comprehensive but accessib...\n", "Stats: {'total_messages': 3, 'estimated_tokens': 670, 'has_summary': False}\n", "------------------------------\n", "Turn 2: What are neural networks?\n", "Response: <think>\n", "This is a follow-up question about neural networks, which are fundamental to deep learning a...\n", "Stats: {'total_messages': 5, 'estimated_tokens': 1232, 'has_summary': False}\n", "------------------------------\n", "Turn 3: How does deep learning work?\n", "Response: <think>\n", "This question is about deep learning, which builds on the neural network concept I just expl...\n", "Stats: {'total_messages': 8, 'estimated_tokens': 2220, 'has_summary': True}\n", "------------------------------\n", "Turn 4: What about natural language processing?\n", "Response: <think>\n", "Now the user is asking about natural language processing (NLP), which is how computers under...\n", "Stats: {'total_messages': 11, 'estimated_tokens': 3105, 'has_summary': True}\n", "------------------------------\n", "Turn 5: Can you explain transformers?\n", "Response: <think>\n", "The user is asking about transformer models, which are the foundation of modern large langua...\n", "Stats: {'total_messages': 12, 'estimated_tokens': 3517, 'has_summary': True}\n", "------------------------------\n", "Turn 6: What is attention mechanism?\n", "Response: <think>\n", "This is a technical question about attention mechanisms, which are central to transformer mo...\n", "Stats: {'total_messages': 12, 'estimated_tokens': 4195, 'has_summary': True}\n", "------------------------------\n", "Turn 7: How does this relate to what we discussed about machine learning earlier?\n", "Response: <think>\n", "The user is asking me to connect the attention mechanism to our earlier discussions about ma...\n", "Stats: {'total_messages': 12, 'estimated_tokens': 4330, 'has_summary': True}\n", "------------------------------\n"]}], "source": ["class SmartConversationManager:\n", "    def __init__(self, system_message, model=\"Dhanishtha-2.0-preview\", max_context_messages=20):\n", "        self.system_message = system_message\n", "        self.messages = [{\"role\": \"system\", \"content\": system_message}]\n", "        self.model = model\n", "        self.max_context_messages = max_context_messages\n", "        self.hai = HAI()\n", "        self.conversation_summary = \"\"\n", "    \n", "    def _estimate_tokens(self, text):\n", "        \"\"\"Rough token estimation (1 token ≈ 4 characters)\"\"\"\n", "        return len(text) // 4\n", "    \n", "    def _summarize_old_messages(self):\n", "        \"\"\"Summarize older messages to maintain context\"\"\"\n", "        if len(self.messages) > self.max_context_messages:\n", "            # Keep system message and recent messages\n", "            old_messages = self.messages[1:-10]  # Skip system, keep last 10\n", "            \n", "            # Create summary prompt\n", "            summary_content = \"\\n\".join([f\"{msg['role']}: {msg['content']}\" for msg in old_messages])\n", "            \n", "            summary_response = self.hai.chat.completions.create(\n", "                model=self.model,\n", "                messages=[\n", "                    {\n", "                        \"role\": \"user\", \n", "                        \"content\": f\"Summarize this conversation concisely, preserving key information:\\n\\n{summary_content}\"\n", "                    }\n", "                ],\n", "                temperature=0.3,\n", "                max_tokens=200\n", "            )\n", "            \n", "            self.conversation_summary = summary_response.choices[0].message.content\n", "            \n", "            # Rebuild messages with summary\n", "            updated_system = f\"{self.system_message}\\n\\nPrevious conversation summary: {self.conversation_summary}\"\n", "            self.messages = [\n", "                {\"role\": \"system\", \"content\": updated_system}\n", "            ] + self.messages[-10:]  # Keep last 10 messages\n", "    \n", "    def add_user_message(self, content):\n", "        self.messages.append({\"role\": \"user\", \"content\": content})\n", "        self._summarize_old_messages()\n", "    \n", "    def get_response(self, temperature=0.7):\n", "        response = self.hai.chat.completions.create(\n", "            model=self.model,\n", "            messages=self.messages,\n", "            temperature=temperature\n", "        )\n", "        \n", "        assistant_message = response.choices[0].message.content\n", "        self.messages.append({\"role\": \"assistant\", \"content\": assistant_message})\n", "        \n", "        return assistant_message\n", "    \n", "    def get_stats(self):\n", "        total_tokens = sum(self._estimate_tokens(msg[\"content\"]) for msg in self.messages)\n", "        return {\n", "            \"total_messages\": len(self.messages),\n", "            \"estimated_tokens\": total_tokens,\n", "            \"has_summary\": bool(self.conversation_summary)\n", "        }\n", "\n", "# Demo smart conversation management\n", "smart_chat = SmartConversationManager(\n", "    \"You are a helpful assistant that remembers context from our conversation.\",\n", "    max_context_messages=5  # Small limit for demo\n", ")\n", "\n", "print(\"🧠 Smart Context Management Demo:\")\n", "print(\"=\" * 40)\n", "\n", "# Simulate a longer conversation\n", "topics = [\n", "    \"Tell me about machine learning\",\n", "    \"What are neural networks?\",\n", "    \"How does deep learning work?\",\n", "    \"What about natural language processing?\",\n", "    \"Can you explain transformers?\",\n", "    \"What is attention mechanism?\",\n", "    \"How does this relate to what we discussed about machine learning earlier?\"\n", "]\n", "\n", "for i, topic in enumerate(topics, 1):\n", "    smart_chat.add_user_message(topic)\n", "    response = smart_chat.get_response()\n", "    stats = smart_chat.get_stats()\n", "    \n", "    print(f\"Turn {i}: {topic}\")\n", "    print(f\"Response: {response[:100]}...\")\n", "    print(f\"Stats: {stats}\")\n", "    print(\"-\" * 30)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Advanced Prompting Techniques\n", "\n", "Sophisticated prompting strategies for better results."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔗 Chain of Thought Response:\n", "========================================\n", "<think>\n", "This is a multi-step math problem about calculating change after a purchase. I need to work through it methodically, following the steps outlined in the request. The problem involves calculating the cost of fruits and then determining change from a $20 bill. Let me break this down carefully.\n", "</think>\n", "\n", "Let me solve this step-by-step:\n", "\n", "1. **Understanding the problem**: We need to find out how much change someone receives after buying 4 pounds of apples at $2 per pound and 2 pounds of oranges at $3 per pound, paying with a $20 bill.\n", "\n", "2. **Identifying given information**:\n", "   - Apples cost $2 per pound\n", "   - Oranges cost $3 per pound\n", "   - 4 pounds of apples purchased\n", "   - 2 pounds of oranges purchased\n", "   - Payment amount: $20\n", "\n", "3. **Determining the approach**: I'll calculate the cost of each fruit type separately, add them together to get the total cost, and then subtract that from the $20 to find the change.\n", "\n", "4. **Working through the solution**:\n", "   - Cost of apples: 4 pounds × $2 per pound = $8\n", "   - Cost of oranges: 2 pounds × $3 per pound = $6\n", "   - Total cost: $8 + $6 = $14\n", "   - Change received: $20 - $14 = $6\n", "\n", "5. **Verifying the answer**: \n", "   - The calculation seems straightforward: (4 × $2) + (2 × $3) = $8 + $6 = $14\n", "   - The change from $20 would be $20 - $14 = $6\n", "   - This answer makes sense because we're paying exactly the amount needed for the purchase.\n", "\n", "The person receives $6 in change after their purchase.\n"]}], "source": ["# Chain of Thought Prompting\n", "def chain_of_thought_prompt(problem):\n", "    prompt = f\"\"\"\n", "    Let's solve this step by step:\n", "    \n", "    Problem: {problem}\n", "    \n", "    Please:\n", "    1. First, understand what is being asked\n", "    2. Identify the given information\n", "    3. Determine what approach to use\n", "    4. Work through the solution step by step\n", "    5. Verify your answer makes sense\n", "    \"\"\"\n", "    return prompt\n", "\n", "# Test chain of thought\n", "problem = \"A store sells apples for $2 per pound and oranges for $3 per pound. If someone buys 4 pounds of apples and 2 pounds of oranges, and pays with a $20 bill, how much change do they receive?\"\n", "\n", "response = hai.chat.completions.create(\n", "    model=\"Dhanishtha-2.0-preview\",\n", "    messages=[{\"role\": \"user\", \"content\": chain_of_thought_prompt(problem)}],\n", "    temperature=0.3\n", ")\n", "\n", "print(\"🔗 Chain of Thought Response:\")\n", "print(\"=\" * 40)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🎯 Few-Shot Classification:\n", "========================================\n", "Classification: Negative\n"]}], "source": ["# Few-Shot Learning\n", "def few_shot_classification():\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"You are a sentiment classifier. Classify text as Positive, Negative, or Neutral.\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"I love this new restaurant! The food is amazing.\"\n", "        },\n", "        {\n", "            \"role\": \"assistant\",\n", "            \"content\": \"Positive\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"The service was terrible and the food was cold.\"\n", "        },\n", "        {\n", "            \"role\": \"assistant\",\n", "            \"content\": \"Negative\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"The weather is okay today.\"\n", "        },\n", "        {\n", "            \"role\": \"assistant\",\n", "            \"content\": \"Neutral\"\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"I lost my job :(\"\n", "        }\n", "    ]\n", "    \n", "    response = hai.chat.completions.create(\n", "        model=\"Helpingai3-raw\",\n", "        messages=messages,\n", "        temperature=0.2\n", "    )\n", "    \n", "    return response.choices[0].message.content\n", "\n", "print(\"\\n🎯 Few-Shot Classification:\")\n", "print(\"=\" * 40)\n", "result = few_shot_classification()\n", "print(f\"Classification: {result}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔄 Conversation Patterns\n", "\n", "Common conversation patterns and how to implement them."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🤔 Socratic Method Demo:\n", "========================================\n", "Tutor: <think>\n", "This is a request to use the Socratic method to teach about photosynthesis. I need to ask thoughtful questions that guide the user to discover the concept themselves rather than simply explaining it. I should start with foundational questions about what they know, then build toward the complexity of photosynthesis.\n", "</think>\n", "\n", "That's a wonderful approach to learning! Let's explore photosynthesis together through questions.\n", "\n", "First, I'm curious - what have you observed about plants? Do you notice anything about how they grow or what they need to thrive?\n", "\n", "<think>\n", "Now I should build on their observations about plants. I'll connect their intuitive understanding to the science of photosynthesis by asking about energy, light, and the transformation process. I need to be careful not to provide direct answers but to guide their thinking toward the concept.\n", "</think>\n", "\n", "You mentioned plants growing. What do you think is their primary source of energy? And how might they use sunlight?\n", "\n", "I notice you're thinking about energy conversion. That's exactly where we're heading. Let's explore this further - what happens to a plant when it's placed in complete darkness for several days? What changes would you expect to see?\n", "\n", "Student: Plants use sunlight to make food?\n", "\n", "Tutor: <think>\n", "This is about designing a follow-up question for a student who asked about plants using sunlight to make food. I need to create a question that encourages deeper thinking about photosynthesis while maintaining a Socratic approach. The question should build on their knowledge and prompt them to explore the process more thoroughly.\n", "</think>\n", "\n", "\n", "That's a great starting point! You mentioned plants using sunlight to make food, which is essentially photosynthesis. Let me ask you something that might help us understand this process better: \n", "\n", "Where exactly does this food-making process happen in the plant, and how do you think the plant actually uses sunlight to create energy? \n", "\n", "<think>\n", "I should add another layer to this question to encourage more specific thinking about the process. I want to prompt them to consider the components involved in photosynthesis beyond just sunlight.\n", "</think>\n", "\n", "\n", "Actually, let's go a step deeper. Photosynthesis requires more than just sunlight. What other materials do you think plants need to perform this process, and what happens to the sunlight at a molecular level? This will help us understand the complete picture of how plants create their food.\n"]}], "source": ["# Socratic Method - Teaching through questions\n", "def socratic_tutor(topic, student_response=None):\n", "    if student_response is None:\n", "        # Initial question\n", "        prompt = f\"I want to learn about {topic}. Instead of just explaining it, please ask me questions to guide my understanding using the Socratic method.\"\n", "    else:\n", "        # Follow-up based on student response\n", "        prompt = f\"Student response: {student_response}\\n\\nPlease ask a follow-up question to deepen understanding about {topic}.\"\n", "    \n", "    response = hai.chat.completions.create(\n", "        model=\"Dhanishtha-2.0-preview\",\n", "        messages=[\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": \"You are a Socratic tutor. Guide learning through thoughtful questions rather than direct explanations. Build on student responses to deepen understanding.\"\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": prompt\n", "            }\n", "        ],\n", "        temperature=0.7\n", "    )\n", "    \n", "    return response.choices[0].message.content\n", "\n", "print(\"🤔 Socratic Method Demo:\")\n", "print(\"=\" * 40)\n", "\n", "# Start learning about photosynthesis\n", "initial_question = socratic_tutor(\"photosynthesis\")\n", "print(f\"Tutor: {initial_question}\\n\")\n", "\n", "# Simulate student response\n", "student_answer = \"Plants use sunlight to make food?\"\n", "print(f\"Student: {student_answer}\\n\")\n", "\n", "# Follow-up question\n", "follow_up = socratic_tutor(\"photosynthesis\", student_answer)\n", "print(f\"Tutor: {follow_up}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Conversation Analytics\n", "\n", "Analyzing conversation patterns and quality."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Conversation Analytics:\n", "========================================\n", "Total Turns: 2\n", "Avg User Message Length: 126.0\n", "Avg Assistant Message Length: 1728.5\n", "User Questions: 2\n", "Assistant Questions: 2\n", "Positive Sentiment Indicators: 1\n", "Negative Sentiment Indicators: 0\n", "Engagement Ratio: 1.0\n"]}], "source": ["def analyze_conversation(messages):\n", "    \"\"\"Analyze conversation metrics\"\"\"\n", "    user_messages = [msg for msg in messages if msg[\"role\"] == \"user\"]\n", "    assistant_messages = [msg for msg in messages if msg[\"role\"] == \"assistant\"]\n", "    \n", "    # Basic metrics\n", "    total_turns = len(user_messages)\n", "    avg_user_length = sum(len(msg[\"content\"]) for msg in user_messages) / len(user_messages) if user_messages else 0\n", "    avg_assistant_length = sum(len(msg[\"content\"]) for msg in assistant_messages) / len(assistant_messages) if assistant_messages else 0\n", "    \n", "    # Question analysis\n", "    user_questions = sum(msg[\"content\"].count(\"?\") for msg in user_messages)\n", "    assistant_questions = sum(msg[\"content\"].count(\"?\") for msg in assistant_messages)\n", "    \n", "    # Sentiment indicators (simple)\n", "    positive_words = [\"great\", \"good\", \"excellent\", \"amazing\", \"wonderful\", \"thank\"]\n", "    negative_words = [\"bad\", \"terrible\", \"awful\", \"hate\", \"wrong\", \"confused\"]\n", "    \n", "    all_text = \" \".join(msg[\"content\"].lower() for msg in messages)\n", "    positive_count = sum(all_text.count(word) for word in positive_words)\n", "    negative_count = sum(all_text.count(word) for word in negative_words)\n", "    \n", "    return {\n", "        \"total_turns\": total_turns,\n", "        \"avg_user_message_length\": round(avg_user_length, 1),\n", "        \"avg_assistant_message_length\": round(avg_assistant_length, 1),\n", "        \"user_questions\": user_questions,\n", "        \"assistant_questions\": assistant_questions,\n", "        \"positive_sentiment_indicators\": positive_count,\n", "        \"negative_sentiment_indicators\": negative_count,\n", "        \"engagement_ratio\": round(assistant_questions / max(total_turns, 1), 2)\n", "    }\n", "\n", "# Analyze our problem-solving conversation\n", "conversation_stats = analyze_conversation(problem_solver.messages)\n", "\n", "print(\"📊 Conversation Analytics:\")\n", "print(\"=\" * 40)\n", "for metric, value in conversation_stats.items():\n", "    print(f\"{metric.replace('_', ' ').title()}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Key Takeaways\n", "\n", "From this exploration of advanced chat completions:\n", "\n", "✅ **System Messages**: Define clear roles and behaviors  \n", "✅ **Multi-turn Context**: Maintain conversation flow and memory  \n", "✅ **Context Management**: Handle long conversations efficiently  \n", "✅ **Advanced Prompting**: Use techniques like chain-of-thought and few-shot learning  \n", "✅ **Conversation Patterns**: Implement educational and therapeutic patterns  \n", "✅ **Analytics**: Monitor conversation quality and engagement  \n", "\n", "## 🚀 Best Practices\n", "\n", "- **Clear System Messages**: Define the AI's role and behavior explicitly\n", "- **Context Awareness**: Maintain relevant conversation history\n", "- **Progressive Disclosure**: Build complexity gradually in conversations\n", "- **Engagement Techniques**: Use questions and interactive elements\n", "- **Quality Monitoring**: Track conversation metrics for improvement\n", "\n", "## 📚 Next Steps\n", "\n", "- **[03-streaming.ipynb](03-streaming.ipynb)** - Real-time streaming responses\n", "- **[04-parameters.ipynb](04-parameters.ipynb)** - Fine-tuning AI behavior\n", "- **[../advanced/](../advanced/)** - Advanced applications and integrations\n", "\n", "---\n", "\n", "**Master the art of AI conversation! 💬✨**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}