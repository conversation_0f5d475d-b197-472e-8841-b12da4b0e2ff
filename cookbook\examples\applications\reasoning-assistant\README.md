# Reasoning Assistant with Dhanishta 2.0 🧠

A powerful reasoning assistant that leverages Dhanishta 2.0's intermediate thinking capabilities to help solve complex problems, provide step-by-step explanations, and offer transparent reasoning processes.

## 🌟 Features

- **Transparent Reasoning**: See how the AI thinks through problems
- **Step-by-Step Solutions**: Break down complex problems into manageable steps
- **Self-Correction**: AI identifies and fixes its own mistakes
- **Multiple Problem Types**: Math, logic, science, programming, and more
- **Interactive Interface**: Easy-to-use command-line and web interfaces
- **Export Results**: Save reasoning processes for later review

## 🚀 Quick Start

### Installation

```bash
pip install HelpingAI streamlit
```

### Basic Usage

```python
from reasoning_assistant import ReasoningAssistant

# Initialize the assistant
assistant = ReasoningAssistant()

# Solve a problem with visible reasoning
result = assistant.solve_problem(
    "If it takes 5 machines 5 minutes to make 5 widgets, "
    "how long would it take 100 machines to make 100 widgets?",
    show_thinking=True
)

print(result)
```

### Web Interface

```bash
streamlit run web_app.py
```

## 📁 Files

- `reasoning_assistant.py` - Core reasoning assistant class
- `web_app.py` - Streamlit web interface
- `cli.py` - Command-line interface
- `examples.py` - Example problems and solutions
- `utils.py` - Utility functions

## 🎯 Use Cases

### 📚 Educational Support
- Homework help with step-by-step explanations
- Concept clarification
- Problem-solving tutorials

### 🔬 Research Assistance
- Literature review analysis
- Hypothesis evaluation
- Experimental design

### 💼 Business Problem Solving
- Strategic planning
- Process optimization
- Decision analysis

### 🧮 Mathematical Reasoning
- Complex calculations
- Proof verification
- Statistical analysis

## 📖 Examples

### Example 1: Mathematical Problem

```python
problem = "A train travels 120 km in the first 2 hours, then 180 km in the next 3 hours. What's the average speed?"

solution = assistant.solve_problem(problem, show_thinking=True)
```

**Output:**
```
🧠 THINKING PROCESS:
<think>
I need to find the average speed for the entire journey.

Total distance = 120 km + 180 km = 300 km
Total time = 2 hours + 3 hours = 5 hours

Average speed = Total distance / Total time
Average speed = 300 km / 5 hours = 60 km/h
</think>

📊 SOLUTION:
The average speed is 60 km/h.

To calculate this:
- Total distance: 120 km + 180 km = 300 km
- Total time: 2 hours + 3 hours = 5 hours
- Average speed: 300 km ÷ 5 hours = 60 km/h
```

### Example 2: Logic Puzzle

```python
problem = """
Three friends have different colored hats: red, blue, and green.
- Alice doesn't have red
- Bob doesn't have blue  
- Charlie doesn't have green
Who has which color?
"""

solution = assistant.solve_problem(problem, show_thinking=True)
```

### Example 3: Scientific Explanation

```python
problem = "Why do objects fall at the same rate in a vacuum regardless of their mass?"

solution = assistant.solve_problem(problem, show_thinking=True, domain="physics")
```

## 🔧 Configuration

### Model Settings

```python
assistant = ReasoningAssistant(
    model="Dhanishtha-2.0-preview",
    temperature=0.3,  # Lower for logical reasoning
    max_tokens=2048,  # Allow space for thinking
    show_thinking_by_default=True
)
```

### Problem Types

```python
# Mathematical problems
assistant.solve_math_problem(problem)

# Logic puzzles
assistant.solve_logic_puzzle(problem)

# Scientific questions
assistant.explain_science_concept(concept)

# Programming challenges
assistant.solve_coding_problem(problem)
```

## 🎨 Web Interface Features

The Streamlit web app provides:

- **Problem Input**: Text area for entering problems
- **Thinking Toggle**: Show/hide reasoning process
- **Problem Categories**: Math, Logic, Science, Programming
- **Solution Export**: Download solutions as PDF or text
- **History**: View previous problems and solutions
- **Settings**: Customize AI parameters

## 📊 Advanced Features

### Custom Reasoning Prompts

```python
assistant.set_reasoning_style("step_by_step")
assistant.set_reasoning_style("socratic_method")
assistant.set_reasoning_style("scientific_method")
```

### Multi-Language Support

```python
# Solve problems in different languages
solution = assistant.solve_problem(
    "¿Cuál es la raíz cuadrada de 144?",
    language="spanish"
)
```

### Collaborative Reasoning

```python
# Multiple AI perspectives
solutions = assistant.collaborative_solve(
    problem,
    perspectives=["mathematical", "practical", "theoretical"]
)
```

## 🛠️ API Reference

### ReasoningAssistant Class

```python
class ReasoningAssistant:
    def __init__(self, model="Dhanishtha-2.0-preview", **kwargs):
        """Initialize the reasoning assistant"""
    
    def solve_problem(self, problem, show_thinking=True, domain=None):
        """Solve a general problem with reasoning"""
    
    def solve_math_problem(self, problem):
        """Specialized mathematical problem solving"""
    
    def solve_logic_puzzle(self, problem):
        """Solve logic puzzles and riddles"""
    
    def explain_concept(self, concept, level="intermediate"):
        """Explain concepts with reasoning"""
    
    def verify_solution(self, problem, solution):
        """Verify if a solution is correct"""
```

## 🧪 Testing

Run the test suite:

```bash
python -m pytest tests/
```

Example tests:
- Mathematical accuracy
- Logic puzzle solving
- Reasoning quality
- Error handling

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your improvements
4. Write tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🔗 Related Examples

- [Educational Tutor](../educational-tutor/) - Personalized learning assistant
- [Creative Writing Aid](../creative-writing/) - Story and content creation
- [Chatbot Builder](../chatbot/) - Intelligent conversation system

## 🎯 Getting Started

1. **Clone or download** this example
2. **Install dependencies**: `pip install HelpingAI streamlit`
3. **Set your API key**: `export HAI_API_KEY=your-key-here`
4. **Run the web app**: `streamlit run web_app.py`
5. **Or use CLI**: `python cli.py`

---

**Build smarter reasoning applications with Dhanishta 2.0! 🧠✨**
