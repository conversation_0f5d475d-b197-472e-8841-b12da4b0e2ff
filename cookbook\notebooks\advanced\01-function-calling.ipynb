import os
import json
import requests
from datetime import datetime, timedelta
from HelpingAI import HAI
import math

# Initialize the client
hai = HAI()

print("🛠️ Ready to explore function calling!")

# Mathematical functions
def calculate_compound_interest(principal, rate, time, compound_frequency=1):
    """Calculate compound interest"""
    amount = principal * (1 + rate/compound_frequency) ** (compound_frequency * time)
    interest = amount - principal
    return {
        "principal": principal,
        "rate": rate,
        "time": time,
        "compound_frequency": compound_frequency,
        "final_amount": round(amount, 2),
        "interest_earned": round(interest, 2)
    }

def calculate_loan_payment(principal, annual_rate, years):
    """Calculate monthly loan payment"""
    monthly_rate = annual_rate / 12
    num_payments = years * 12
    
    if monthly_rate == 0:
        payment = principal / num_payments
    else:
        payment = principal * (monthly_rate * (1 + monthly_rate)**num_payments) / ((1 + monthly_rate)**num_payments - 1)
    
    total_paid = payment * num_payments
    total_interest = total_paid - principal
    
    return {
        "monthly_payment": round(payment, 2),
        "total_paid": round(total_paid, 2),
        "total_interest": round(total_interest, 2)
    }

# Utility functions
def get_current_time():
    """Get current date and time"""
    now = datetime.now()
    return {
        "current_time": now.strftime("%Y-%m-%d %H:%M:%S"),
        "day_of_week": now.strftime("%A"),
        "timezone": "UTC"
    }

def calculate_age(birth_date):
    """Calculate age from birth date (YYYY-MM-DD format)"""
    try:
        birth = datetime.strptime(birth_date, "%Y-%m-%d")
        today = datetime.now()
        age = today.year - birth.year - ((today.month, today.day) < (birth.month, birth.day))
        
        return {
            "birth_date": birth_date,
            "current_age": age,
            "days_lived": (today - birth).days
        }
    except ValueError:
        return {"error": "Invalid date format. Use YYYY-MM-DD"}

# Data analysis functions
def analyze_numbers(numbers):
    """Analyze a list of numbers"""
    if not numbers:
        return {"error": "No numbers provided"}
    
    sorted_nums = sorted(numbers)
    n = len(numbers)
    
    # Calculate statistics
    mean = sum(numbers) / n
    median = sorted_nums[n//2] if n % 2 == 1 else (sorted_nums[n//2-1] + sorted_nums[n//2]) / 2
    
    # Mode calculation
    from collections import Counter
    counts = Counter(numbers)
    max_count = max(counts.values())
    modes = [num for num, count in counts.items() if count == max_count]
    
    # Standard deviation
    variance = sum((x - mean) ** 2 for x in numbers) / n
    std_dev = math.sqrt(variance)
    
    return {
        "count": n,
        "sum": sum(numbers),
        "mean": round(mean, 2),
        "median": median,
        "mode": modes[0] if len(modes) == 1 else modes,
        "min": min(numbers),
        "max": max(numbers),
        "range": max(numbers) - min(numbers),
        "std_deviation": round(std_dev, 2)
    }

print("✅ Functions defined successfully!")

# Define function schemas for the AI
function_schemas = [
    {
        "name": "calculate_compound_interest",
        "description": "Calculate compound interest for an investment",
        "parameters": {
            "type": "object",
            "properties": {
                "principal": {
                    "type": "number",
                    "description": "Initial investment amount"
                },
                "rate": {
                    "type": "number",
                    "description": "Annual interest rate (as decimal, e.g., 0.05 for 5%)"
                },
                "time": {
                    "type": "number",
                    "description": "Time period in years"
                },
                "compound_frequency": {
                    "type": "number",
                    "description": "Number of times interest compounds per year (default: 1)",
                    "default": 1
                }
            },
            "required": ["principal", "rate", "time"]
        }
    },
    {
        "name": "calculate_loan_payment",
        "description": "Calculate monthly payment for a loan",
        "parameters": {
            "type": "object",
            "properties": {
                "principal": {
                    "type": "number",
                    "description": "Loan amount"
                },
                "annual_rate": {
                    "type": "number",
                    "description": "Annual interest rate (as decimal)"
                },
                "years": {
                    "type": "number",
                    "description": "Loan term in years"
                }
            },
            "required": ["principal", "annual_rate", "years"]
        }
    },
    {
        "name": "get_current_time",
        "description": "Get the current date and time",
        "parameters": {
            "type": "object",
            "properties": {},
            "required": []
        }
    },
    {
        "name": "calculate_age",
        "description": "Calculate age from birth date",
        "parameters": {
            "type": "object",
            "properties": {
                "birth_date": {
                    "type": "string",
                    "description": "Birth date in YYYY-MM-DD format"
                }
            },
            "required": ["birth_date"]
        }
    },
    {
        "name": "analyze_numbers",
        "description": "Perform statistical analysis on a list of numbers",
        "parameters": {
            "type": "object",
            "properties": {
                "numbers": {
                    "type": "array",
                    "items": {"type": "number"},
                    "description": "List of numbers to analyze"
                }
            },
            "required": ["numbers"]
        }
    }
]

# Function registry for easy lookup
function_registry = {
    "calculate_compound_interest": calculate_compound_interest,
    "calculate_loan_payment": calculate_loan_payment,
    "get_current_time": get_current_time,
    "calculate_age": calculate_age,
    "analyze_numbers": analyze_numbers
}

print("📋 Function schemas defined!")
print(f"Available functions: {list(function_registry.keys())}")