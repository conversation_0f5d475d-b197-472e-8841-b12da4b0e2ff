{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON>sh<PERSON> 2.0: Multilingual Reasoning 🌍\n", "\n", "Dhanishta 2.0 supports reasoning in 39+ languages while maintaining consistent quality and showing its thinking process across different linguistic contexts. This notebook explores multilingual reasoning capabilities.\n", "\n", "## 🎯 What You'll Explore\n", "- Reasoning in multiple languages\n", "- Cross-language consistency\n", "- Cultural context awareness\n", "- Code-switching capabilities\n", "- Language-specific problem solving"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 Ready to explore multilingual reasoning!\n"]}], "source": ["import os\n", "from HelpingAI import HAI\n", "\n", "# Initialize the client\n", "hai = HAI()\n", "\n", "def multilingual_reasoning(prompt, language_name, temperature=0.3):\n", "    \"\"\"Helper function for multilingual reasoning demonstrations\"\"\"\n", "    print(f\"🌍 {language_name} Reasoning:\")\n", "    print(f\"📝 Prompt: {prompt}\")\n", "    print(\"=\" * 60)\n", "    \n", "    response = hai.chat.completions.create(\n", "        model=\"Dhanishtha-2.0-preview\",\n", "        messages=[{\"role\": \"user\", \"content\": prompt}],\n", "        hide_think=False,\n", "        temperature=temperature,\n", "        max_tokens=2000\n", "    )\n", "    \n", "    print(response.choices[0].message.content)\n", "    print(\"\\n\" + \"=\" * 60 + \"\\n\")\n", "    return response\n", "\n", "print(\"🌍 Ready to explore multilingual reasoning!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🇪🇸 Spanish Reasoning\n", "\n", "Let's start with mathematical and logical reasoning in Spanish."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 Spanish Reasoning:\n", "📝 Prompt: ¿Cuántas letras 'a' hay en la palabra 'banana'? Muestra tu proceso de pensamiento paso a paso.\n", "============================================================\n", "<think>\n", "The user is asking me to count the number of 'a' letters in the word 'banana'. This is a straightforward task that requires careful inspection of each letter. I need to look at the word letter by letter and identify which ones are 'a'. The word is 'banana', so I'll go through it systematically.\n", "</think>\n", "\n", "<think>\n", "Let me break down the word 'banana' letter by letter:\n", "1. First letter: 'b' - not an 'a'\n", "2. Second letter: 'a' - this is an 'a' (count = 1)\n", "3. Third letter: 'n' - not an 'a'\n", "4. Fourth letter: 'a' - this is an 'a' (count = 2)\n", "5. Fifth letter: 'n' - not an 'a'\n", "6. Sixth letter: 'a' - this is an 'a' (count = 3)\n", "\n", "So there are 3 'a' letters in the word 'banana'.\n", "</think>\n", "\n", "Para contar las letras 'a' en la palabra 'banana', voy a analizar cada letra individualmente:\n", "\n", "La palabra es: b-a-n-a-n-a\n", "\n", "Voy a identificar cada posición:\n", "1. Posición 1: 'b' - no es una 'a'\n", "2. Posición 2: 'a' - encontré una 'a' (1)\n", "3. Posición 3: 'n' - no es una 'a'\n", "4. Posición 4: 'a' - encontré otra 'a' (2)\n", "5. Posición 5: 'n' - no es una 'a'\n", "6. Posi<PERSON> 6: 'a' - encontré otra 'a' (3)\n", "\n", "Después de revisar cada letra cuidadosamente, puedo confirmar que la palabra 'banana' contiene 3 letras 'a'.\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-3e18697b424748a4b514627573fae04b', created=1752231450220, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content=\"<think>\\nThe user is asking me to count the number of 'a' letters in the word 'banana'. This is a straightforward task that requires careful inspection of each letter. I need to look at the word letter by letter and identify which ones are 'a'. The word is 'banana', so I'll go through it systematically.\\n</think>\\n\\n<think>\\nLet me break down the word 'banana' letter by letter:\\n1. First letter: 'b' - not an 'a'\\n2. Second letter: 'a' - this is an 'a' (count = 1)\\n3. Third letter: 'n' - not an 'a'\\n4. Fourth letter: 'a' - this is an 'a' (count = 2)\\n5. Fifth letter: 'n' - not an 'a'\\n6. Sixth letter: 'a' - this is an 'a' (count = 3)\\n\\nSo there are 3 'a' letters in the word 'banana'.\\n</think>\\n\\nPara contar las letras 'a' en la palabra 'banana', voy a analizar cada letra individualmente:\\n\\nLa palabra es: b-a-n-a-n-a\\n\\nVoy a identificar cada posición:\\n1. Posición 1: 'b' - no es una 'a'\\n2. Posición 2: 'a' - encontré una 'a' (1)\\n3. Posición 3: 'n' - no es una 'a'\\n4. Posición 4: 'a' - encontré otra 'a' (2)\\n5. Posición 5: 'n' - no es una 'a'\\n6. Posición 6: 'a' - encontré otra 'a' (3)\\n\\nDespués de revisar cada letra cuidadosamente, puedo confirmar que la palabra 'banana' contiene 3 letras 'a'.\", function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=386, prompt_tokens=34, total_tokens=420, prompt_tokens_details=None))"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Mathematical reasoning in Spanish\n", "multilingual_reasoning(\n", "    \"¿Cuántas letras 'a' hay en la palabra 'banana'? Muestra tu proceso de pensamiento paso a paso.\",\n", "    \"Spanish\"\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 Spanish Reasoning:\n", "📝 Prompt: Si un tren viaja a 80 km/h y otro tren viaja a 120 km/h en direcciones opuestas, y están inicialmente separados por 300 km, ¿en cuánto tiempo se encontrarán? Resuelve paso a paso.\n", "============================================================\n", "<think>\n", "Este es un problema de movimiento relativo. Tengo dos trenes que viajan en direcciones opuestas, lo que significa que se acercan el uno al otro. Necesito calcular cuándo se encontrarán. Voy a resolverlo paso a paso.\n", "\n", "Primero, identificaré los datos:\n", "- Tren 1: velocidad = 80 km/h\n", "- Tren 2: velocidad = 120 km/h\n", "- Distancia inicial entre ellos = 300 km\n", "</think>\n", "\n", "<think>\n", "Cuando dos objetos se mueven en direcciones opuestas, sus velocidades se suman para calcular la velocidad relativa. Esto significa que el tren 1 se acerca al tren 2 a una velocidad de 80 km/h, y el tren 2 se acerca al tren 1 a 120 km/h. <PERSON><PERSON> lo tanto, la velocidad relativa total es:\n", "\n", "Velocidad relativa = 80 km/h + 120 km/h = 200 km/h\n", "\n", "Ahora, para calcular el tiempo, uso la fórmula:\n", "Tiempo = Distancia ÷ Velocidad relativa\n", "</think>\n", "\n", "<think>\n", "Sustituyendo los valores:\n", "Tiempo = 300 km ÷ 200 km/h = 1.5 horas\n", "\n", "Para verificar: \n", "- En 1.5 horas, el tren 1 recorrerá: 80 km/h × 1.5 h = 120 km\n", "- En 1.5 horas, el tren 2 recorrerá: 120 km/h × 1.5 h = 180 km\n", "- Distancia total recorrida: 120 km + 180 km = 300 km ✓\n", "\n", "Por lo tanto, los trenes se encontrarán después de 1.5 horas.\n", "</think>\n", "\n", "Para resolver este problema, necesito analizar cómo cambia la distancia entre los trenes con el tiempo.\n", "\n", "Cuando dos objetos se mueven en direcciones opuestas, sus velocidades se suman para calcular la velocidad a la que se acercan. En este caso:\n", "\n", "- Tren 1: 80 km/h\n", "- Tren 2: 120 km/h\n", "- Velocidad relativa: 80 km/h + 120 km/h = 200 km/h\n", "\n", "La distancia inicial entre los trenes es de 300 km, y están acercándose a razón de 200 km por hora.\n", "\n", "Para calcular el tiempo hasta que se encuentren, uso la fórmula:\n", "Tiempo = Distancia ÷ Velocidad relativa\n", "\n", "Tiempo = 300 km ÷ 200 km/h = 1.5 horas\n", "\n", "Por lo tanto, los trenes se encontrarán después de 1 hora y 30 minutos.\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-89d407fdf88c447ca68f66bbd712c288', created=1752231492086, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content='<think>\\nEste es un problema de movimiento relativo. Tengo dos trenes que viajan en direcciones opuestas, lo que significa que se acercan el uno al otro. Necesito calcular cuándo se encontrarán. Voy a resolverlo paso a paso.\\n\\nPrimero, identificaré los datos:\\n- Tren 1: velocidad = 80 km/h\\n- Tren 2: velocidad = 120 km/h\\n- Distancia inicial entre ellos = 300 km\\n</think>\\n\\n<think>\\nCuando dos objetos se mueven en direcciones opuestas, sus velocidades se suman para calcular la velocidad relativa. Esto significa que el tren 1 se acerca al tren 2 a una velocidad de 80 km/h, y el tren 2 se acerca al tren 1 a 120 km/h. Por lo tanto, la velocidad relativa total es:\\n\\nVelocidad relativa = 80 km/h + 120 km/h = 200 km/h\\n\\nAhora, para calcular el tiempo, uso la fórmula:\\nTiempo = Distancia ÷ Velocidad relativa\\n</think>\\n\\n<think>\\nSustituyendo los valores:\\nTiempo = 300 km ÷ 200 km/h = 1.5 horas\\n\\nPara verificar: \\n- En 1.5 horas, el tren 1 recorrerá: 80 km/h × 1.5 h = 120 km\\n- En 1.5 horas, el tren 2 recorrerá: 120 km/h × 1.5 h = 180 km\\n- Distancia total recorrida: 120 km + 180 km = 300 km ✓\\n\\nPor lo tanto, los trenes se encontrarán después de 1.5 horas.\\n</think>\\n\\nPara resolver este problema, necesito analizar cómo cambia la distancia entre los trenes con el tiempo.\\n\\nCuando dos objetos se mueven en direcciones opuestas, sus velocidades se suman para calcular la velocidad a la que se acercan. En este caso:\\n\\n- Tren 1: 80 km/h\\n- Tren 2: 120 km/h\\n- Velocidad relativa: 80 km/h + 120 km/h = 200 km/h\\n\\nLa distancia inicial entre los trenes es de 300 km, y están acercándose a razón de 200 km por hora.\\n\\nPara calcular el tiempo hasta que se encuentren, uso la fórmula:\\nTiempo = Distancia ÷ Velocidad relativa\\n\\nTiempo = 300 km ÷ 200 km/h = 1.5 horas\\n\\nPor lo tanto, los trenes se encontrarán después de 1 hora y 30 minutos.', function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=620, prompt_tokens=66, total_tokens=686, prompt_tokens_details=None))"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Complex math problem in Spanish\n", "multilingual_reasoning(\n", "    \"Si un tren viaja a 80 km/h y otro tren viaja a 120 km/h en direcciones opuestas, \"\n", "    \"y están inicialmente separados por 300 km, ¿en cuánto tiempo se encontrarán? \"\n", "    \"Resuelve paso a paso.\",\n", "    \"Spanish\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🇫🇷 French Reasoning\n", "\n", "Mathematical and scientific reasoning in French."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 French Reasoning:\n", "📝 Prompt: Un rectangle a une longueur de 12 cm et une largeur de 8 cm. Calculez son aire et son périmètre. Montrez vos calculs étape par étape.\n", "============================================================\n", "<think>\n", "Je dois calculer l'aire et le périmètre d'un rectangle avec une longueur de 12 cm et une largeur de 8 cm. Pour l'aire, je multiplierai la longueur par la largeur. Pour le périmètre, je calculerai la somme de tous les côtés, ou équivalentement 2 × (longueur + largeur). C'est un calcul simple mais je dois présenter les étapes de manière claire et détaillée.\n", "</think>\n", "\n", "Pour calculer l'aire et le périmètre d'un rectangle avec une longueur de 12 cm et une largeur de 8 cm, je vais procéder étape par étape.\n", "\n", "**Calcul de l'aire:**\n", "L'aire d'un rectangle se calcule en multipliant sa longueur par sa largeur.\n", "Aire = Longueur × <PERSON>ur\n", "Aire = 12 cm × 8 cm\n", "Aire = 96 cm²\n", "\n", "**Cal<PERSON>l du périmètre:**\n", "Le périmètre d'un rectangle se calcule en additionnant toutes ses côtés, ou en utilisant la formule: 2 × (<PERSON><PERSON><PERSON> + <PERSON><PERSON>).\n", "Périmètre = 2 × (<PERSON><PERSON><PERSON> + <PERSON><PERSON>)\n", "Périmètre = 2 × (12 cm + 8 cm)\n", "Périmètre = 2 × 20 cm\n", "Périmètre = 40 cm\n", "\n", "<PERSON><PERSON>, l'aire du rectangle est de 96 cm² et son périmètre est de 40 cm.\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-e5b12a15924b467ea3b4dfd52ef09291', created=1752231522949, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content=\"<think>\\nJe dois calculer l'aire et le périmètre d'un rectangle avec une longueur de 12 cm et une largeur de 8 cm. Pour l'aire, je multiplierai la longueur par la largeur. Pour le périmètre, je calculerai la somme de tous les côtés, ou équivalentement 2 × (longueur + largeur). C'est un calcul simple mais je dois présenter les étapes de manière claire et détaillée.\\n</think>\\n\\nPour calculer l'aire et le périmètre d'un rectangle avec une longueur de 12 cm et une largeur de 8 cm, je vais procéder étape par étape.\\n\\n**Calcul de l'aire:**\\nL'aire d'un rectangle se calcule en multipliant sa longueur par sa largeur.\\nAire = Longueur × Largeur\\nAire = 12 cm × 8 cm\\nAire = 96 cm²\\n\\n**Calcul du périmètre:**\\nLe périmètre d'un rectangle se calcule en additionnant toutes ses côtés, ou en utilisant la formule: 2 × (Longueur + Largeur).\\nPérimètre = 2 × (Longueur + Largeur)\\nPérimètre = 2 × (12 cm + 8 cm)\\nPérimètre = 2 × 20 cm\\nPérimètre = 40 cm\\n\\nDonc, l'aire du rectangle est de 96 cm² et son périmètre est de 40 cm.\", function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=338, prompt_tokens=48, total_tokens=386, prompt_tokens_details=None))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Geometry problem in French\n", "multilingual_reasoning(\n", "    \"Un rectangle a une longueur de 12 cm et une largeur de 8 cm. \"\n", "    \"Calculez son aire et son périmètre. Montrez vos calculs étape par étape.\",\n", "    \"French\"\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 French Reasoning:\n", "📝 Prompt: Expliquez pourquoi la glace flotte sur l'eau du point de vue moléculaire. Décrivez le processus de raisonnement scientifique.\n", "============================================================\n", "<think>\n", "Cette question porte sur l'explication scientifique du phénomène de flottaison de la glace sur l'eau. Il s'agit d'un concept de physique de l'état solide et liquide de l'eau, qui repose sur les propriétés moléculaires. Je dois expliquer ce phénomène de manière scientifiquement précise mais accessible, en détaillant les forces intermoléculaires et leur organisation spatiale.\n", "</think>\n", "\n", "La glace flotte sur l'eau grâce à une propriété unique qui découle de la structure moléculaire de l'eau. Cette capacité à flotter est fondamentalement liée à la densité relative entre l'eau liquide et l'eau solide.\n", "\n", "<think>\n", "Je dois maintenant expliquer les détails moléculaires. L'eau est composée de molécules H₂O avec une structure en V. L'angle de 104,5° entre les atomes d'hydrogène et l'oxygène crée une géométrie asymétrique. Les liaisons hydrogène jouent un rôle crucial dans cette histoire.\n", "</think>\n", "\n", "Au niveau moléculaire, l'eau est formée de molécules H₂O présentant une structure en V avec un angle de 104,5°. Cette géométrie asymétrique, combinée à l'électronégativité différente de l'hydrogène et de l'oxygène, crée un dipôle électrique permanent dans chaque molécule.\n", "\n", "Dans l'état liquide, ces molécules se déplacent librement mais restent en interaction constante via des liaisons hydrogène. Ces liaisons sont plus faibles que les liaisons covalentes internes des molécules, mais elles sont suffisamment fortes pour influencer considérablement le comportement de l'eau.\n", "\n", "<PERSON><PERSON><PERSON> l'eau gèle, elle subit un phénomène particulier: les molécules s'organisent dans un réseau cristallin régulier où chaque molécule d'eau forme environ 4 liaisons hydrogène avec ses voisines. Cette organisation crée un espace vide entre les molécules, car les liaisons hydrogène sont dirigées à angle fixe (environ 109°).\n", "\n", "<think>\n", "Il est crucial d'expliquer pourquoi cette structure cristalline rend la glace moins dense que l'eau liquide, car c'est cette différence de densité qui explique la flottaison. Je dois aussi mentionner l'effet de l'entropie et comment cela diffère de la plupart des substances.\n", "</think>\n", "\n", "Cette organisation en réseau cristallin hexagonal est plus volumineuse que l'arrangement aléatoire des molécules dans l'eau liquide. Lorsque l'eau gèle, les molécules s'éloignent légèrement les unes des autres pour adopter cette structure ordonnée, ce qui augmente le volume tout en maintenant la même masse. Puisque la densité est définie comme la masse divisée par le volume, et que le volume augmente sans que la masse ne change, la densité de la glace devient inférieure à celle de l'eau liquide (environ 0,92 g/cm³ contre 1 g/cm³).\n", "\n", "Cette propriété exceptionnelle de l'eau a des conséquences fondamentales pour notre planète:\n", "- Les glaces flottantes isolent l'eau douce sous-jacente, permettant à celle-ci de rester liquide même en dessous de 0°C\n", "- Les océans ne gèlent que superficiellement, préservant la vie marine\n", "- Cette propriété anomale permet aux écosystèmes aquatiques de survivre aux hivers\n", "\n", "Cette capacité de la glace à flotter est donc bien plus qu'un simple phénomène physique - c'est une condition essentielle pour la vie telle que nous la connaissons sur Terre.\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-0befa513cf2144ac85a946e09897be61', created=1752231602238, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content=\"<think>\\nCette question porte sur l'explication scientifique du phénomène de flottaison de la glace sur l'eau. Il s'agit d'un concept de physique de l'état solide et liquide de l'eau, qui repose sur les propriétés moléculaires. Je dois expliquer ce phénomène de manière scientifiquement précise mais accessible, en détaillant les forces intermoléculaires et leur organisation spatiale.\\n</think>\\n\\nLa glace flotte sur l'eau grâce à une propriété unique qui découle de la structure moléculaire de l'eau. Cette capacité à flotter est fondamentalement liée à la densité relative entre l'eau liquide et l'eau solide.\\n\\n<think>\\nJe dois maintenant expliquer les détails moléculaires. L'eau est composée de molécules H₂O avec une structure en V. L'angle de 104,5° entre les atomes d'hydrogène et l'oxygène crée une géométrie asymétrique. Les liaisons hydrogène jouent un rôle crucial dans cette histoire.\\n</think>\\n\\nAu niveau moléculaire, l'eau est formée de molécules H₂O présentant une structure en V avec un angle de 104,5°. Cette géométrie asymétrique, combinée à l'électronégativité différente de l'hydrogène et de l'oxygène, crée un dipôle électrique permanent dans chaque molécule.\\n\\nDans l'état liquide, ces molécules se déplacent librement mais restent en interaction constante via des liaisons hydrogène. Ces liaisons sont plus faibles que les liaisons covalentes internes des molécules, mais elles sont suffisamment fortes pour influencer considérablement le comportement de l'eau.\\n\\nLorsque l'eau gèle, elle subit un phénomène particulier: les molécules s'organisent dans un réseau cristallin régulier où chaque molécule d'eau forme environ 4 liaisons hydrogène avec ses voisines. Cette organisation crée un espace vide entre les molécules, car les liaisons hydrogène sont dirigées à angle fixe (environ 109°).\\n\\n<think>\\nIl est crucial d'expliquer pourquoi cette structure cristalline rend la glace moins dense que l'eau liquide, car c'est cette différence de densité qui explique la flottaison. Je dois aussi mentionner l'effet de l'entropie et comment cela diffère de la plupart des substances.\\n</think>\\n\\nCette organisation en réseau cristallin hexagonal est plus volumineuse que l'arrangement aléatoire des molécules dans l'eau liquide. Lorsque l'eau gèle, les molécules s'éloignent légèrement les unes des autres pour adopter cette structure ordonnée, ce qui augmente le volume tout en maintenant la même masse. Puisque la densité est définie comme la masse divisée par le volume, et que le volume augmente sans que la masse ne change, la densité de la glace devient inférieure à celle de l'eau liquide (environ 0,92 g/cm³ contre 1 g/cm³).\\n\\nCette propriété exceptionnelle de l'eau a des conséquences fondamentales pour notre planète:\\n- Les glaces flottantes isolent l'eau douce sous-jacente, permettant à celle-ci de rester liquide même en dessous de 0°C\\n- Les océans ne gèlent que superficiellement, préservant la vie marine\\n- Cette propriété anomale permet aux écosystèmes aquatiques de survivre aux hivers\\n\\nCette capacité de la glace à flotter est donc bien plus qu'un simple phénomène physique - c'est une condition essentielle pour la vie telle que nous la connaissons sur Terre.\", function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=859, prompt_tokens=42, total_tokens=901, prompt_tokens_details=None))"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Scientific reasoning in French\n", "multilingual_reasoning(\n", "    \"Expliquez pourquoi la glace flotte sur l'eau du point de vue moléculaire. \"\n", "    \"Dé<PERSON><PERSON>z le processus de raisonnement scientifique.\",\n", "    \"French\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🇩🇪 German Reasoning\n", "\n", "Logical and analytical reasoning in German."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 German Reasoning:\n", "📝 Prompt: Drei Freunde haben verschiedenfarbige Hüte: rot, blau und grün. <PERSON> hat nicht rot, <PERSON> hat nicht blau, und <PERSON> hat nicht grün. Wer hat welche Farbe? <PERSON><PERSON>sen <PERSON>e dies Schritt für Schritt.\n", "============================================================\n", "<think>\n", "This is a logic puzzle in German about three friends with colored hats. I need to determine who has which color hat based on the given constraints. Let me work through this systematically.\n", "\n", "The constraints are:\n", "1. <PERSON> doesn't have a red hat\n", "2. <PERSON> doesn't have a blue hat\n", "3. <PERSON> doesn't have a green hat\n", "\n", "The colors are red, blue, and green. I need to find a consistent assignment.\n", "</think>\n", "\n", "<think>\n", "Let me try different combinations:\n", "\n", "If <PERSON> has blue, then <PERSON> can't have blue, so <PERSON> must have either red or green. If <PERSON> has red, then <PERSON> must have green. But <PERSON> can't have green, so this doesn't work. If <PERSON> has green, then <PERSON> must have red, which works.\n", "\n", "Let me double-check: <PERSON> (blue), <PERSON> (green), <PERSON> (red)\n", "- <PERSON> doesn't have red ✓\n", "- <PERSON> doesn't have blue ✓\n", "- <PERSON> doesn't have green ✓\n", "\n", "This assignment satisfies all constraints!\n", "</think>\n", "\n", "Um dieses Rät<PERSON> zu lösen, werde ich schrittweise vorgehen und die gegebenen Bedingungen analysieren.\n", "\n", "Wir haben drei <PERSON> (<PERSON>, <PERSON>, Charlie) und drei <PERSON>ben (rot, blau, grün) mit diesen Einschränkungen:\n", "- <PERSON> hat nicht rot\n", "- <PERSON> hat nicht blau\n", "- Charlie hat nicht grün\n", "\n", "Schritt 1: <PERSON><PERSON><PERSON><PERSON>\n", "<PERSON> nicht rot haben kann, bleibt nur blau oder grün für sie.\n", "\n", "Schritt 2: <PERSON><PERSON><PERSON><PERSON>\n", "<PERSON> nicht blau haben kann, bleibt nur rot oder grün für ihn.\n", "\n", "Schritt 3: <PERSON><PERSON><PERSON><PERSON>\n", "<PERSON> nicht grün haben kann, bleibt nur rot oder blau für ihn.\n", "\n", "Schritt 4: Teste mögliche Kombinationen\n", "<PERSON><PERSON> wir uns systematisch durch die Möglichkeiten durch:\n", "\n", "<PERSON><PERSON> blau hat:\n", "- <PERSON> könnte rot oder grün haben\n", "- <PERSON> kö<PERSON> rot oder blau haben\n", "\n", "<PERSON><PERSON> grün hat:\n", "- <PERSON> könnte rot oder grün haben\n", "- <PERSON> kö<PERSON> rot oder blau haben\n", "\n", "Schritt 5: Eliminiere widersprüchliche Kombinationen\n", "<PERSON><PERSON> blau hat und Bob grün hat, bleibt nur rot für Charlie. Dies funktioniert!\n", "\n", "<PERSON><PERSON> grün hat und Bob rot hat, bleibt nur blau für Charlie. Dies funktioniert!\n", "\n", "Schritt 6: <PERSON><PERSON><PERSON><PERSON> alle Bedingungen\n", "Für die Kombination Alice=blau, <PERSON>=gr<PERSON><PERSON>, <PERSON>=rot:\n", "- <PERSON> hat nicht rot ✓\n", "- <PERSON> hat nicht blau ✓\n", "- Charlie hat nicht grün ✓\n", "\n", "Für die Kombination Alice=gr<PERSON><PERSON>, <PERSON>=rot, <PERSON>=blau:\n", "- <PERSON> hat nicht rot ✓\n", "- <PERSON> hat nicht blau ✓\n", "- Charlie hat nicht grün ✓\n", "\n", "Beide Kombinationen erfüllen alle Bedingungen. Allerdings, wenn wir die Einschränkung hinzunehmen, dass jeder genau eine Farbe haben muss und jede Farbe genau einmal vergeben wird, dann sind beide Lösungen gültig.\n", "\n", "Die Lösung lautet also:\n", "- <PERSON> hat entweder blau oder grün\n", "- Bob hat entweder rot oder grün\n", "- Charlie hat entweder rot oder blau\n", "\n", "Mit den genannten Einschränkungen gibt es zwei mögliche Lösungen:\n", "1. <PERSON>=blau, <PERSON>=gr<PERSON><PERSON>, <PERSON>=rot\n", "2. <PERSON>=grün, <PERSON>=rot, <PERSON>=blau\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-be7771c48f86498ea955f019b1d6a457', created=1752231625669, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content=\"<think>\\nThis is a logic puzzle in German about three friends with colored hats. I need to determine who has which color hat based on the given constraints. Let me work through this systematically.\\n\\nThe constraints are:\\n1. <PERSON> doesn't have a red hat\\n2. <PERSON> doesn't have a blue hat\\n3. <PERSON> doesn't have a green hat\\n\\nThe colors are red, blue, and green. I need to find a consistent assignment.\\n</think>\\n\\n<think>\\nLet me try different combinations:\\n\\nIf Alice has blue, then <PERSON> can't have blue, so <PERSON> must have either red or green. If <PERSON> has red, then <PERSON> must have green. But <PERSON> can't have green, so this doesn't work. If <PERSON> has green, then <PERSON> must have red, which works.\\n\\nLet me double-check: <PERSON> (blue), <PERSON> (green), <PERSON> (red)\\n- <PERSON> doesn't have red ✓\\n- <PERSON> doesn't have blue ✓\\n- <PERSON> doesn't have green ✓\\n\\nThis assignment satisfies all constraints!\\n</think>\\n\\nUm dieses R<PERSON>sel zu lösen, werde ich schrittweise vorgehen und die gegebenen Bedingungen analysieren.\\n\\nWir haben drei Freunde (Alice, Bob, Charlie) und drei Hutfarben (rot, blau, grün) mit diesen Einschränkungen:\\n- Alice hat nicht rot\\n- Bob hat nicht blau\\n- Charlie hat nicht grün\\n\\nSchritt 1: Betrachte Alice\\nDa Alice nicht rot haben kann, bleibt nur blau oder grün für sie.\\n\\nSchritt 2: Betrachte Bob\\nDa Bob nicht blau haben kann, bleibt nur rot oder grün für ihn.\\n\\nSchritt 3: Betrachte Charlie\\nDa Charlie nicht grün haben kann, bleibt nur rot oder blau für ihn.\\n\\nSchritt 4: Teste mögliche Kombinationen\\nLassen wir uns systematisch durch die Möglichkeiten durch:\\n\\nWenn Alice blau hat:\\n- Bob könnte rot oder grün haben\\n- Charlie könnte rot oder blau haben\\n\\nWenn Alice grün hat:\\n- Bob könnte rot oder grün haben\\n- Charlie könnte rot oder blau haben\\n\\nSchritt 5: Eliminiere widersprüchliche Kombinationen\\nWenn Alice blau hat und Bob grün hat, bleibt nur rot für Charlie. Dies funktioniert!\\n\\nWenn Alice grün hat und Bob rot hat, bleibt nur blau für Charlie. Dies funktioniert!\\n\\nSchritt 6: Prüfe alle Bedingungen\\nFür die Kombination Alice=blau, Bob=grün, Charlie=rot:\\n- Alice hat nicht rot ✓\\n- Bob hat nicht blau ✓\\n- Charlie hat nicht grün ✓\\n\\nFür die Kombination Alice=grün, Bob=rot, Charlie=blau:\\n- Alice hat nicht rot ✓\\n- Bob hat nicht blau ✓\\n- Charlie hat nicht grün ✓\\n\\nBeide Kombinationen erfüllen alle Bedingungen. Allerdings, wenn wir die Einschränkung hinzunehmen, dass jeder genau eine Farbe haben muss und jede Farbe genau einmal vergeben wird, dann sind beide Lösungen gültig.\\n\\nDie Lösung lautet also:\\n- Alice hat entweder blau oder grün\\n- Bob hat entweder rot oder grün\\n- Charlie hat entweder rot oder blau\\n\\nMit den genannten Einschränkungen gibt es zwei mögliche Lösungen:\\n1. Alice=blau, Bob=grün, Charlie=rot\\n2. Alice=grün, Bob=rot, Charlie=blau\", function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=750, prompt_tokens=63, total_tokens=813, prompt_tokens_details=None))"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Logic puzzle in German\n", "multilingual_reasoning(\n", "    \"Drei Freunde haben verschiedenfarbige Hüte: rot, blau und grün. \"\n", "    \"<PERSON> hat nicht rot, <PERSON> hat nicht blau, und Charlie hat nicht grün. \"\n", "    \"Wer hat welche Farbe? <PERSON><PERSON><PERSON> Si<PERSON> dies Schritt für Schritt.\",\n", "    \"German\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🇯🇵 Japanese Reasoning\n", "\n", "Mathematical and logical reasoning in Japanese."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 Japanese Reasoning:\n", "📝 Prompt: 240の15%はいくらですか？計算過程を段階的に示してください。\n", "============================================================\n", "<think>\n", "この質問は240の15%を求める計算問題です。ステップバイステップで解説する必要があります。感情的な要素はないので<ser>ブロックは必要ありません。計算方法は、まず1%の値を求め、それを15倍する方法か、直接15/100を掛ける方法があります。どちらでも構いませんが、直感的な説明を優先したいと思います。\n", "</think>\n", "\n", "240の15%を求めるには、まず1%の値を求めます。\n", "\n", "240 ÷ 100 = 2.4\n", "\n", "この2.4を15倍します：\n", "2.4 × 15 = 36\n", "\n", "したがって、240の15%は36です。\n", "\n", "別の考え方として、240 × 0.15 = 36 と計算することもできます。\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-95549b0a31004bb1a8c0b43a4c021872', created=1752231630577, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content='<think>\\nこの質問は240の15%を求める計算問題です。ステップバイステップで解説する必要があります。感情的な要素はないので<ser>ブロックは必要ありません。計算方法は、まず1%の値を求め、それを15倍する方法か、直接15/100を掛ける方法があります。どちらでも構いませんが、直感的な説明を優先したいと思います。\\n</think>\\n\\n240の15%を求めるには、まず1%の値を求めます。\\n\\n240 ÷ 100 = 2.4\\n\\nこの2.4を15倍します：\\n2.4 × 15 = 36\\n\\nしたがって、240の15%は36です。\\n\\n別の考え方として、240 × 0.15 = 36 と計算することもできます。', function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=198, prompt_tokens=28, total_tokens=226, prompt_tokens_details=None))"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Mathematical reasoning in Japanese\n", "multilingual_reasoning(\n", "    \"240の15%はいくらですか？計算過程を段階的に示してください。\",\n", "    \"Japanese\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🇰🇷 Korean Reasoning\n", "\n", "Problem-solving in Korean with step-by-step thinking."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 Korean Reasoning:\n", "📝 Prompt: 사라는 오렌지보다 사과를 두 배 많이 가지고 있습니다. 그녀는 바나나보다 오렌지를 3개 더 많이 가지고 있습니다. 바나나가 15개라면, 총 과일은 몇 개입니까? 단계별로 풀어주세요.\n", "============================================================\n", "<think>\n", "This is a math problem in Korean. I need to solve it step by step. Let me translate and understand the problem first:\n", "- <PERSON> has twice as many apples as oranges\n", "- She has 3 more oranges than bananas\n", "- If she has 15 bananas, how many fruits does she have in total?\n", "</think>\n", "\n", "<think>\n", "Let me solve this systematically:\n", "1. <PERSON><PERSON><PERSON> = 15 (given)\n", "2. <PERSON><PERSON> = Bananas + 3 = 15 + 3 = 18\n", "3. Apples = 2 × Oranges = 2 × 18 = 36\n", "4. Total fruits = Bananas + Oranges + Apples = 15 + 18 + 36 = 69\n", "</think>\n", "\n", "이 문제를 단계별로 풀어보겠습니다.\n", "\n", "1. 먼저 주어진 정보를 정리해 봅니다:\n", "   - 바나나의 수는 15개입니다\n", "   - 오렌지의 수는 바나나보다 3개 더 많습니다\n", "   - 사과의 수는 오렌지의 수보다 두 배 많습니다\n", "\n", "2. 오렌지의 수를 계산합니다:\n", "   오렌지 = 바나나 + 3 = 15 + 3 = 18개\n", "\n", "3. 사과의 수를 계산합니다:\n", "   사과 = 2 × 오렌지 = 2 × 18 = 36개\n", "\n", "4. 총 과일의 수를 계산합니다:\n", "   총 과일 = 바나나 + 오렌지 + 사과 = 15 + 18 + 36 = 69개\n", "\n", "따라서 사라가 가진 총 과일의 수는 69개입니다.\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-51f249277d554bd9a95c9412438318e1', created=1752231708973, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content='<think>\\nThis is a math problem in Korean. I need to solve it step by step. Let me translate and understand the problem first:\\n- <PERSON> has twice as many apples as oranges\\n- She has 3 more oranges than bananas\\n- If she has 15 bananas, how many fruits does she have in total?\\n</think>\\n\\n<think>\\nLet me solve this systematically:\\n1. Bananas = 15 (given)\\n2. Oranges = Bananas + 3 = 15 + 3 = 18\\n3. Apples = 2 × Oranges = 2 × 18 = 36\\n4. Total fruits = Bananas + Oranges + Apples = 15 + 18 + 36 = 69\\n</think>\\n\\n이 문제를 단계별로 풀어보겠습니다.\\n\\n1. 먼저 주어진 정보를 정리해 봅니다:\\n   - 바나나의 수는 15개입니다\\n   - 오렌지의 수는 바나나보다 3개 더 많습니다\\n   - 사과의 수는 오렌지의 수보다 두 배 많습니다\\n\\n2. 오렌지의 수를 계산합니다:\\n   오렌지 = 바나나 + 3 = 15 + 3 = 18개\\n\\n3. 사과의 수를 계산합니다:\\n   사과 = 2 × 오렌지 = 2 × 18 = 36개\\n\\n4. 총 과일의 수를 계산합니다:\\n   총 과일 = 바나나 + 오렌지 + 사과 = 15 + 18 + 36 = 69개\\n\\n따라서 사라가 가진 총 과일의 수는 69개입니다.', function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=389, prompt_tokens=74, total_tokens=463, prompt_tokens_details=None))"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Word problem in Korean\n", "multilingual_reasoning(\n", "    \"사라는 오렌지보다 사과를 두 배 많이 가지고 있습니다. \"\n", "    \"그녀는 바나나보다 오렌지를 3개 더 많이 가지고 있습니다. \"\n", "    \"바나나가 15개라면, 총 과일은 몇 개입니까? 단계별로 풀어주세요.\",\n", "    \"Korean\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🇨🇳 Chinese Reasoning\n", "\n", "Mathematical and logical reasoning in Chinese."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 Chinese (Simplified) Reasoning:\n", "📝 Prompt: 如果5台机器在5分钟内制造5个小部件，那么100台机器制造100个小部件需要多长时间？请逐步解释你的推理过程。\n", "============================================================\n", "<think>\n", "这个问题是一个关于生产率和时间的数学问题。我需要仔细分析题目中的关系。题目说5台机器在5分钟内制造5个小部件。首先，我需要确定每台机器的生产率，然后计算100台机器制造100个小部件需要的时间。\n", "</think>\n", "\n", "<think>\n", "让我逐步推理：\n", "1. 5台机器在5分钟内制造5个小部件\n", "2. 每台机器在5分钟内制造1个小部件（5÷5=1）\n", "3. 每台机器的生产率是0.2个小部件/分钟（1÷5=0.2）\n", "4. 100台机器的总生产率是20个小部件/分钟（100×0.2=20）\n", "5. 制造100个小部件需要的时间是5分钟（100÷20=5）\n", "</think>\n", "\n", "这个问题需要仔细分析生产率和时间的关系。\n", "\n", "首先，根据题目信息：\n", "- 5台机器在5分钟内制造5个小部件\n", "- 这意味着每台机器在5分钟内制造1个小部件\n", "- 因此每台机器的生产率为0.2个小部件/分钟\n", "\n", "当有100台机器时，它们的总生产率为：\n", "- 100台机器 × 0.2个小部件/分钟 = 20个小部件/分钟\n", "\n", "现在要制造100个小部件，所需时间为：\n", "- 100个小部件 ÷ 20个小部件/分钟 = 5分钟\n", "\n", "因此，100台机器制造100个小部件需要5分钟。\n", "\n", "这个结果可能看起来有些反直觉，但这是因为增加机器数量的同时也增加了要生产的小部件数量，而这两者的比例保持不变。生产率仍然是每个时间单位生产20个小部件，所以时间保持不变。\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-01616f236ee4480fb24c2d51b9264a7b', created=1752231772249, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content='<think>\\n这个问题是一个关于生产率和时间的数学问题。我需要仔细分析题目中的关系。题目说5台机器在5分钟内制造5个小部件。首先，我需要确定每台机器的生产率，然后计算100台机器制造100个小部件需要的时间。\\n</think>\\n\\n<think>\\n让我逐步推理：\\n1. 5台机器在5分钟内制造5个小部件\\n2. 每台机器在5分钟内制造1个小部件（5÷5=1）\\n3. 每台机器的生产率是0.2个小部件/分钟（1÷5=0.2）\\n4. 100台机器的总生产率是20个小部件/分钟（100×0.2=20）\\n5. 制造100个小部件需要的时间是5分钟（100÷20=5）\\n</think>\\n\\n这个问题需要仔细分析生产率和时间的关系。\\n\\n首先，根据题目信息：\\n- 5台机器在5分钟内制造5个小部件\\n- 这意味着每台机器在5分钟内制造1个小部件\\n- 因此每台机器的生产率为0.2个小部件/分钟\\n\\n当有100台机器时，它们的总生产率为：\\n- 100台机器 × 0.2个小部件/分钟 = 20个小部件/分钟\\n\\n现在要制造100个小部件，所需时间为：\\n- 100个小部件 ÷ 20个小部件/分钟 = 5分钟\\n\\n因此，100台机器制造100个小部件需要5分钟。\\n\\n这个结果可能看起来有些反直觉，但这是因为增加机器数量的同时也增加了要生产的小部件数量，而这两者的比例保持不变。生产率仍然是每个时间单位生产20个小部件，所以时间保持不变。', function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=395, prompt_tokens=44, total_tokens=439, prompt_tokens_details=None))"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Mathematical reasoning in Chinese (Simplified)\n", "multilingual_reasoning(\n", "    \"如果5台机器在5分钟内制造5个小部件，那么100台机器制造100个小部件需要多长时间？\"\n", "    \"请逐步解释你的推理过程。\",\n", "    \"Chinese (Simplified)\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🇮🇳 Hindi Reasoning\n", "\n", "Problem-solving in Hindi with cultural context."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 Hindi Reasoning:\n", "📝 Prompt: एक दुकानदार ने 100 रुपये में 20 आम खरीदे और उन्हें 150 रुपये में बेच दिया। उसका लाभ प्रतिशत क्या है? कृपया अपनी गणना दिखाएं।\n", "============================================================\n", "<think>\n", "This is a profit percentage calculation problem in Hindi. Let me break it down:\n", "- Cost price (CP) of 20 mangoes = ₹100\n", "- Selling price (SP) of 20 mangoes = ₹150\n", "- Profit = SP - CP = ₹150 - ₹100 = ₹50\n", "- Profit percentage = (Profit/CP) × 100 = (50/100) × 100 = 50%\n", "</think>\n", "\n", "इस प्रश्न को हल करने के लिए मैं लाभ प्रतिशत की गणना करूंगी।\n", "\n", "दिया गया है:\n", "- 20 आम खरीदने का लागत मूल्य (CP) = ₹100\n", "- 20 आमों का विक्रय मूल्य (SP) = ₹150\n", "\n", "<think>\n", "अब मैं लाभ की गणना करूंगी और फिर लाभ प्रतिशत निकालूंगी।\n", "लाभ = विक्रय मूल्य - लागत मूल्य\n", "लाभ प्रतिशत = (लाभ/लागत मूल्य) × 100\n", "</think>\n", "\n", "लाभ की गणना:\n", "- लाभ = विक्रय मूल्य - लागत मूल्य\n", "- लाभ = ₹150 - ₹100 = ₹50\n", "\n", "लाभ प्रतिशत की गणना:\n", "- लाभ प्रतिशत = (लाभ/लागत मूल्य) × 100\n", "- लाभ प्रतिशत = (₹50/₹100) × 100\n", "- लाभ प्रतिशत = 0.5 × 100 = 50%\n", "\n", "इसलिए, दुकानदार का लाभ प्रतिशत 50% है।\n", "\n", "यह एक सरल लेकिन महत्वपूर्ण गणितीय अवधारणा है जो व्यापार में बहुत उपयोगी होती है। जब कोई व्यक्ति या व्यापार अपने निवेश पर कितना लाभ कमा रहा है, यह पता लगाने के लिए लाभ प्रतिशत की गणना करना आवश्यक होता है।\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-18f4b1a4ea274fabbf488b5fd094efb5', created=1752231816081, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content='<think>\\nThis is a profit percentage calculation problem in Hindi. Let me break it down:\\n- Cost price (CP) of 20 mangoes = ₹100\\n- Selling price (SP) of 20 mangoes = ₹150\\n- Profit = SP - CP = ₹150 - ₹100 = ₹50\\n- Profit percentage = (Profit/CP) × 100 = (50/100) × 100 = 50%\\n</think>\\n\\nइस प्रश्न को हल करने के लिए मैं लाभ प्रतिशत की गणना करूंगी।\\n\\nदिया गया है:\\n- 20 आम खरीदने का लागत मूल्य (CP) = ₹100\\n- 20 आमों का विक्रय मूल्य (SP) = ₹150\\n\\n<think>\\nअब मैं लाभ की गणना करूंगी और फिर लाभ प्रतिशत निकालूंगी।\\nलाभ = विक्रय मूल्य - लागत मूल्य\\nलाभ प्रतिशत = (लाभ/लागत मूल्य) × 100\\n</think>\\n\\nलाभ की गणना:\\n- लाभ = विक्रय मूल्य - लागत मूल्य\\n- लाभ = ₹150 - ₹100 = ₹50\\n\\nलाभ प्रतिशत की गणना:\\n- लाभ प्रतिशत = (लाभ/लागत मूल्य) × 100\\n- लाभ प्रतिशत = (₹50/₹100) × 100\\n- लाभ प्रतिशत = 0.5 × 100 = 50%\\n\\nइसलिए, दुकानदार का लाभ प्रतिशत 50% है।\\n\\nयह एक सरल लेकिन महत्वपूर्ण गणितीय अवधारणा है जो व्यापार में बहुत उपयोगी होती है। जब कोई व्यक्ति या व्यापार अपने निवेश पर कितना लाभ कमा रहा है, यह पता लगाने के लिए लाभ प्रतिशत की गणना करना आवश्यक होता है।', function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=753, prompt_tokens=127, total_tokens=880, prompt_tokens_details=None))"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Mathematical reasoning in Hindi\n", "multilingual_reasoning(\n", "    \"एक दुकानदार ने 100 रुपये में 20 आम खरीदे और उन्हें 150 रुपये में बेच दिया। \"\n", "    \"उसका लाभ प्रतिशत क्या है? कृपया अपनी गणना दिखाएं।\",\n", "    \"Hindi\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🇦🇪 Arabic Reasoning\n", "\n", "Mathematical and logical reasoning in Arabic."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 Arabic Reasoning:\n", "📝 Prompt: إذا كان لدى أحمد ضعف عدد الكتب التي لدى فاطمة، ولدى فاطمة 3 كتب أكثر من علي، وإذا كان لدى علي 12 كتاباً، فكم كتاباً لدى أحمد؟ اشرح خطوات الحل.\n", "============================================================\n", "<think>\n", "This is a math problem in Arabic. I need to solve it step by step to find how many books <PERSON> has. Let me translate and work through this systematically.\n", "</think>\n", "\n", "<think>\n", "Let's identify what we know:\n", "- <PERSON> has 12 books\n", "- <PERSON><PERSON> has 3 more books than <PERSON>\n", "- <PERSON> has twice as many books as <PERSON><PERSON>\n", "\n", "So <PERSON><PERSON> has 12 + 3 = 15 books.\n", "And <PERSON> has 2 × 15 = 30 books.\n", "</think>\n", "\n", "لحل هذه المسألة، سأتبع الخطوات التالية:\n", "\n", "1. **تحديد عدد كتب علي**: لدينا معلومة مباشرة أن علي لديه 12 كتاباً.\n", "\n", "2. **حسا<PERSON> عدد كتب فاطمة**: نعلم أن فاطمة لديها 3 كتب أكثر من علي.\n", "   إذن: عدد كتب فاطمة = 12 + 3 = 15 كتاباً.\n", "\n", "3. **حساب عد<PERSON> كتب أحمد**: نعلم أن أحمد لديه ضعف عدد كتب فاطمة.\n", "   إذن: عدد كتب أحمد = 2 × 15 = 30 كتاباً.\n", "\n", "لذلك، لدى أحمد 30 كتاباً.\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-d04a1e44782c430d88e27a97445bd4ea', created=1752231822394, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content=\"<think>\\nThis is a math problem in Arabic. I need to solve it step by step to find how many books <PERSON> has. Let me translate and work through this systematically.\\n</think>\\n\\n<think>\\nLet's identify what we know:\\n- <PERSON> has 12 books\\n- <PERSON><PERSON> has 3 more books than <PERSON>\\<PERSON><PERSON> <PERSON> has twice as many books as <PERSON><PERSON>\\n\\nSo Fatima has 12 + 3 = 15 books.\\nAnd <PERSON> has 2 × 15 = 30 books.\\n</think>\\n\\nلحل هذه المسألة، سأتبع الخطوات التالية:\\n\\n1. **تحديد عدد كتب علي**: لدينا معلومة مباشرة أن علي لديه 12 كتاباً.\\n\\n2. **حساب عدد كتب فاطمة**: نعلم أن فاطمة لديها 3 كتب أكثر من علي.\\n   إذن: عدد كتب فاطمة = 12 + 3 = 15 كتاباً.\\n\\n3. **حساب عدد كتب أحمد**: نعلم أن أحمد لديه ضعف عدد كتب فاطمة.\\n   إذن: عدد كتب أحمد = 2 × 15 = 30 كتاباً.\\n\\nلذلك، لدى أحمد 30 كتاباً.\", function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=260, prompt_tokens=63, total_tokens=323, prompt_tokens_details=None))"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Mathematical reasoning in Arabic\n", "multilingual_reasoning(\n", "    \"إذا كان لدى أحمد ضعف عدد الكتب التي لدى فاطمة، ولدى فاطمة 3 كتب أكثر من علي، \"\n", "    \"وإذا كان لدى علي 12 كتاباً، فكم كتاباً لدى أحمد؟ اشرح خطوات الحل.\",\n", "    \"Arabic\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔄 Cross-Language Consistency Test\n", "\n", "Let's test the same problem in multiple languages to verify consistency."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🌍 Testing in English:\n", "----------------------------------------\n", "Answer: To find 25% of 80, I'll work through this step by step:\n", "\n", "First, I need to convert 25% to a decimal f...\n", "✅ Correct answer (20) found: 20\n", "\n", "🌍 Testing in Spanish:\n", "----------------------------------------\n", "Answer: Para calcular el 25% de 80, primero debo convertir el porcentaje a decimal.\n", "\n", "El 25% en forma decimal...\n", "✅ Correct answer (20) found: 20\n", "\n", "🌍 Testing in French:\n", "----------------------------------------\n", "Answer: Pour calculer 25% de 80, je vais procéder étape par étape:\n", "\n", "1. <PERSON><PERSON><PERSON>bor<PERSON>, je convertis le pourcentage ...\n", "✅ Correct answer (20) found: 20\n", "\n", "🌍 Testing in German:\n", "----------------------------------------\n", "Answer: Um 25% von 80 zu berechnen, gehe ich Schritt für Schritt vor:\n", "\n", "1. <PERSON>uerst verwandle ich den Prozentwe...\n", "✅ Correct answer (20) found: 20\n", "\n", "🌍 Testing in Chinese:\n", "----------------------------------------\n", "Answer: 要计算80的25%，我需要一步步来思考。\n", "\n", "首先，百分比的意思是\\\"每一百的部分\\\"。所以25%就是25/100，也就是0.25。\n", "\n", "接下来，我需要将80乘以0.25：\n", "80 × 0.25 = 20\n", "...\n", "✅ Correct answer (20) found: 20\n", "\n", "📊 CONSISTENCY ANALYSIS:\n", "==================================================\n", "All languages should arrive at the same numerical result (20).\n", "The reasoning process should be logically equivalent across languages.\n"]}], "source": ["def test_cross_language_consistency():\n", "    \"\"\"Test the same problem in multiple languages\"\"\"\n", "    \n", "    # Same mathematical problem in different languages\n", "    problems = {\n", "        \"English\": \"What is 25% of 80? Show your calculation step by step.\",\n", "        \"Spanish\": \"¿Cuál es el 25% de 80? Muestra tu cálculo paso a paso.\",\n", "        \"French\": \"Combien font 25% de 80? Montrez votre calcul étape par étape.\",\n", "        \"German\": \"Was sind 25% von 80? Zeigen Sie Ihre Berechnung Schritt für Schritt.\",\n", "        \"Chinese\": \"80的25%是多少？请逐步显示你的计算过程。\"\n", "    }\n", "    \n", "    results = {}\n", "    \n", "    for language, problem in problems.items():\n", "        print(f\"\\n🌍 Testing in {language}:\")\n", "        print(\"-\" * 40)\n", "        \n", "        response = hai.chat.completions.create(\n", "            model=\"Dhanishtha-2.0-preview\",\n", "            messages=[{\"role\": \"user\", \"content\": problem}],\n", "            hide_think=True,  # Focus on final answers for comparison\n", "            temperature=0.2,  # Low temperature for consistency\n", "            max_tokens=500\n", "        )\n", "        \n", "        content = response.choices[0].message.content\n", "        results[language] = content\n", "        \n", "        # Extract numerical answer if possible\n", "        import re\n", "        numbers = re.findall(r'\\b20\\b|\\b20\\.0\\b|\\bveinte\\b|\\bvingt\\b|\\bzwanzig\\b|\\b二十\\b', content.lower())\n", "        \n", "        print(f\"Answer: {content[:100]}...\")\n", "        if numbers:\n", "            print(f\"✅ Correct answer (20) found: {numbers[0]}\")\n", "        else:\n", "            print(\"❓ Answer format varies\")\n", "    \n", "    print(\"\\n📊 CONSISTENCY ANALYSIS:\")\n", "    print(\"=\" * 50)\n", "    print(\"All languages should arrive at the same numerical result (20).\")\n", "    print(\"The reasoning process should be logically equivalent across languages.\")\n", "\n", "test_cross_language_consistency()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔀 Code-Switching Capabilities\n", "\n", "Dhanishta 2.0 can handle mixed-language inputs and respond appropriately."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 English-Spanish Mix Reasoning:\n", "📝 Prompt: I need to calculate el 30% de 150. Can you help me solve this paso a paso?\n", "============================================================\n", "<think>\n", "This is a straightforward percentage calculation question. The user wants to find 30% of 150 and needs a step-by-step explanation. The question is in Spanish, so I should respond in Spanish as well. I'll need to explain how to convert a percentage to a decimal and then multiply by the original number.\n", "</think>\n", "\n", "Por supuesto, te ayudaré a calcular el 30% de 150 paso a paso.\n", "\n", "<think>\n", "Para calcular el 30% de 150, primero convertimos el porcentaje a decimal dividiendo entre 100: 30 ÷ 100 = 0.3. Luego multiplicamos este valor por el número original: 150 × 0.3 = 45. Podría también explicar otro método usando fracciones para verificar.\n", "</think>\n", "\n", "Para calcular el 30% de 150, seguimos estos pasos:\n", "\n", "1. **Convertir el porcentaje a decimal**: Dividimos 30 entre 100.\n", "   \\\\[30 \\\\div 100 = 0.3\\\\]\n", "\n", "2. **Multiplicar por el número original**: Multiplicamos 150 por 0.3.\n", "   \\\\[150 \\\\times 0.3 = 45\\\\]\n", "\n", "**Respuesta final**: El 30% de 150 es **45**.\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-2b4fc162ceb342619383cf559de9e389', created=1752231868575, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content=\"<think>\\nThis is a straightforward percentage calculation question. The user wants to find 30% of 150 and needs a step-by-step explanation. The question is in Spanish, so I should respond in Spanish as well. I'll need to explain how to convert a percentage to a decimal and then multiply by the original number.\\n</think>\\n\\nPor supuesto, te ayudaré a calcular el 30% de 150 paso a paso.\\n\\n<think>\\nPara calcular el 30% de 150, primero convertimos el porcentaje a decimal dividiendo entre 100: 30 ÷ 100 = 0.3. Luego multiplicamos este valor por el número original: 150 × 0.3 = 45. Podría también explicar otro método usando fracciones para verificar.\\n</think>\\n\\nPara calcular el 30% de 150, seguimos estos pasos:\\n\\n1. **Convertir el porcentaje a decimal**: Dividimos 30 entre 100.\\n   \\\\\\\\[30 \\\\\\\\div 100 = 0.3\\\\\\\\]\\n\\n2. **Multiplicar por el número original**: Multiplicamos 150 por 0.3.\\n   \\\\\\\\[150 \\\\\\\\times 0.3 = 45\\\\\\\\]\\n\\n**Respuesta final**: El 30% de 150 es **45**.\", function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=305, prompt_tokens=33, total_tokens=338, prompt_tokens_details=None))"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Mixed English-Spanish problem\n", "multilingual_reasoning(\n", "    \"I need to calculate el 30% de 150. Can you help me solve this paso a paso?\",\n", "    \"English-Spanish Mix\"\n", ")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 English-French Mix Reasoning:\n", "📝 Prompt: Please explain comment calculer the area of un rectangle with longueur 15 cm et largeur 8 cm.\n", "============================================================\n", "<think>\n", "This question is asking me to explain how to calculate the area of a rectangle with length 15 cm and width 8 cm. The question is in French, so I should respond in French as well. I need to explain the formula for calculating the area of a rectangle and then apply it to these specific dimensions.\n", "</think>\n", "\n", "Pour calculer l'aire d'un rectangle, nous utilisons la formule suivante:\n", "\n", "Aire = Longueur × <PERSON>ur\n", "\n", "Dans notre cas, nous avons:\n", "- Longueur = 15 cm\n", "- Largeur = 8 cm\n", "\n", "En appliquant la formule:\n", "Aire = 15 cm × 8 cm = 120 cm²\n", "\n", "<PERSON><PERSON>, l'aire de ce rectangle est égale à 120 centimètres carrés.\n", "\n", "<think>\n", "Je peux ajouter quelques explications supplémentaires pour mieux expliquer le concept et s'assurer que la personne comprend bien comment calculer l'aire d'un rectangle.\n", "</think>\n", "\n", "Pour mieux comprendre, imaginez que vous avez un rectangle que vous pouvez diviser en carrés de 1 cm de côté. Si la longueur est de 15 cm, vous pouvez placer 15 carrés côte à côte. Et si la largeur est de 8 cm, vous pouvez faire 8 rangées de ces carrés. Au total, vous aurez 15 × 8 = 120 carrés de 1 cm² chacun, donc une surface totale de 120 cm².\n", "\n", "Cette formule est universelle pour tous les rectangles, quelles que soient leurs dimensions, tant que vous mesurez la longueur et la largeur dans la même unité (centimètres, mètres, etc.).\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-7454acfe498d438cbe093624222ff841', created=1752231885148, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content=\"<think>\\nThis question is asking me to explain how to calculate the area of a rectangle with length 15 cm and width 8 cm. The question is in French, so I should respond in French as well. I need to explain the formula for calculating the area of a rectangle and then apply it to these specific dimensions.\\n</think>\\n\\nPour calculer l'aire d'un rectangle, nous utilisons la formule suivante:\\n\\nAire = Longueur × Largeur\\n\\nDans notre cas, nous avons:\\n- Longueur = 15 cm\\n- Largeur = 8 cm\\n\\nEn appliquant la formule:\\nAire = 15 cm × 8 cm = 120 cm²\\n\\nDonc, l'aire de ce rectangle est égale à 120 centimètres carrés.\\n\\n<think>\\nJe peux ajouter quelques explications supplémentaires pour mieux expliquer le concept et s'assurer que la personne comprend bien comment calculer l'aire d'un rectangle.\\n</think>\\n\\nPour mieux comprendre, imaginez que vous avez un rectangle que vous pouvez diviser en carrés de 1 cm de côté. Si la longueur est de 15 cm, vous pouvez placer 15 carrés côte à côte. Et si la largeur est de 8 cm, vous pouvez faire 8 rangées de ces carrés. Au total, vous aurez 15 × 8 = 120 carrés de 1 cm² chacun, donc une surface totale de 120 cm².\\n\\nCette formule est universelle pour tous les rectangles, quelles que soient leurs dimensions, tant que vous mesurez la longueur et la largeur dans la même unité (centimètres, mètres, etc.).\", function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=373, prompt_tokens=32, total_tokens=405, prompt_tokens_details=None))"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# Mixed English-French problem\n", "multilingual_reasoning(\n", "    \"Please explain comment calculer the area of un rectangle with longueur 15 cm et largeur 8 cm.\",\n", "    \"English-French Mix\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎭 Cultural Context Awareness\n", "\n", "Testing how well Dhanishta 2.0 handles culturally-specific problems."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 Japanese (Cultural Context) Reasoning:\n", "📝 Prompt: 日本の学校で、1クラス30人の生徒がいます。そのうち60%が数学が好きで、40%が理科が好きです。両方好きな生徒が25%いる場合、数学も理科も好きでない生徒は何人いますか？\n", "============================================================\n", "<think>\n", "この問題は集合の包含排除原理を使って解くことができます。まず与えられた情報を整理します：\n", "- 1クラス30人の生徒\n", "- 数学が好きな生徒：60%（30人×0.6=18人）\n", "- 理科が好きな生徒：40%（30人×0.4=12人）\n", "- 数学も理科も好きな生徒：25%（30人×0.25=7.5人）\n", "- 質問：両方好きでない生徒は何人か？\n", "</think>\n", "\n", "この問題を解くために、まず与えられた情報を整理していきます。\n", "\n", "<think>\n", "包含排除原理を使うと、数学か理科か好きな生徒の数は：\n", "(数学が好きな人数) + (理科が好きな人数) - (両方好きな人数) = 18 + 12 - 7.5 = 22.5人\n", "となります。ただし、人数は整数なので、7.5人という不自然な数値が出てきました。これは問題の設定に誤りがある可能性があります。\n", "</think>\n", "\n", "まず、1クラス30人の生徒について考えます。\n", "\n", "数学が好きな生徒は30人の60%、つまり18人います。\n", "理科が好きな生徒は30人の40%、つまり12人います。\n", "数学も理科も好きな生徒は30人の25%、つまり7.5人います。\n", "\n", "<think>\n", "この問題には不自然な点があります。生徒の数は整数でなければなりません。7.5人という数値は現実的ではありません。これは問題の設定に誤りがあるか、四捨五入されている可能性があります。\n", "\n", "しかし、問題を解く上では、7.5人とします。数学か理科か好きな生徒の数は18 + 12 - 7.5 = 22.5人です。両方好きでない生徒の数は30 - 22.5 = 7.5人となります。\n", "\n", "ただし、現実的な解釈としては、両方好きな生徒が7人または8人と解釈するのが妥当かもしれません。その場合、両方好きでない生徒は30 - (18 + 12 - 7) = 7人または30 - (18 + 12 - 8) = 8人となります。\n", "</think>\n", "\n", "数学か理科か好きな生徒の数を求めるには、包含排除原理を使います。\n", "\n", "数学が好きな生徒の数 + 理科が好きな生徒の数 - 両方好きな生徒の数\n", "= 18人 + 12人 - 7.5人\n", "= 22.5人\n", "\n", "したがって、両方好きでない生徒の数は：\n", "30人 - 22.5人 = 7.5人\n", "\n", "この結果は少しひっかかりますが、数学的に正しい答えは7.5人です。ただし、現実的には7人または8人と解釈するのがもっとも自然でしょう。\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-735d7a7337104695a724dbdf554c77cc', created=1752231933536, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content='<think>\\nこの問題は集合の包含排除原理を使って解くことができます。まず与えられた情報を整理します：\\n- 1クラス30人の生徒\\n- 数学が好きな生徒：60%（30人×0.6=18人）\\n- 理科が好きな生徒：40%（30人×0.4=12人）\\n- 数学も理科も好きな生徒：25%（30人×0.25=7.5人）\\n- 質問：両方好きでない生徒は何人か？\\n</think>\\n\\nこの問題を解くために、まず与えられた情報を整理していきます。\\n\\n<think>\\n包含排除原理を使うと、数学か理科か好きな生徒の数は：\\n(数学が好きな人数) + (理科が好きな人数) - (両方好きな人数) = 18 + 12 - 7.5 = 22.5人\\nとなります。ただし、人数は整数なので、7.5人という不自然な数値が出てきました。これは問題の設定に誤りがある可能性があります。\\n</think>\\n\\nまず、1クラス30人の生徒について考えます。\\n\\n数学が好きな生徒は30人の60%、つまり18人います。\\n理科が好きな生徒は30人の40%、つまり12人います。\\n数学も理科も好きな生徒は30人の25%、つまり7.5人います。\\n\\n<think>\\nこの問題には不自然な点があります。生徒の数は整数でなければなりません。7.5人という数値は現実的ではありません。これは問題の設定に誤りがあるか、四捨五入されている可能性があります。\\n\\nしかし、問題を解く上では、7.5人とします。数学か理科か好きな生徒の数は18 + 12 - 7.5 = 22.5人です。両方好きでない生徒の数は30 - 22.5 = 7.5人となります。\\n\\nただし、現実的な解釈としては、両方好きな生徒が7人または8人と解釈するのが妥当かもしれません。その場合、両方好きでない生徒は30 - (18 + 12 - 7) = 7人または30 - (18 + 12 - 8) = 8人となります。\\n</think>\\n\\n数学か理科か好きな生徒の数を求めるには、包含排除原理を使います。\\n\\n数学が好きな生徒の数 + 理科が好きな生徒の数 - 両方好きな生徒の数\\n= 18人 + 12人 - 7.5人\\n= 22.5人\\n\\nしたがって、両方好きでない生徒の数は：\\n30人 - 22.5人 = 7.5人\\n\\nこの結果は少しひっかかりますが、数学的に正しい答えは7.5人です。ただし、現実的には7人または8人と解釈するのがもっとも自然でしょう。', function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=677, prompt_tokens=69, total_tokens=746, prompt_tokens_details=None))"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Cultural context in Japanese\n", "multilingual_reasoning(\n", "    \"日本の学校で、1クラス30人の生徒がいます。そのうち60%が数学が好きで、\"\n", "    \"40%が理科が好きです。両方好きな生徒が25%いる場合、\"\n", "    \"数学も理科も好きでない生徒は何人いますか？\",\n", "    \"Japanese (Cultural Context)\"\n", ")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌍 Hindi (Cultural Context) Reasoning:\n", "📝 Prompt: दीवाली के त्योहार के लिए, राम ने 500 रुपये में मिठाई खरीदी। उसने अपने 4 दोस्तों के साथ बराबर बांटी। अगर हर व्यक्ति को 20 रुपये की मिठाई मिली, तो कुल कितने लोग थे?\n", "============================================================\n", "<think>\n", "This is a math problem in Hindi about Diwali. <PERSON> bought sweets for 500 rupees and shared them equally with 4 friends. If each person got 20 rupees worth of sweets, I need to find the total number of people. Let me work through this step by step.\n", "</think>\n", "\n", "इस प्रश्न को हल करने के लिए मैं धीरे-धीरे सोचूंगी।\n", "\n", "<think>\n", "मुझे दिया गया है कि राम ने कुल 500 रुपये में मिठाई खरीदी और उसने अपने 4 दोस्तों के साथ बराबर बांटी। हर व्यक्ति को 20 रुपये की मिठाई मिली। मुझे कुल लोगों की संख्या निकालनी है।\n", "</think>\n", "\n", "मैं इस समस्या को हल करने के लिए कुल राशि और प्रति व्यक्ति मिठाई के मूल्य का उपयोग करूंगी।\n", "\n", "<think>\n", "कुल मिठाई की कीमत = 500 रुपये\n", "प्रति व्यक्ति मिठाई की कीमत = 20 रुपये\n", "इसलिए, कुल लोगों की संख्या = कुल मिठाई की कीमत ÷ प्रति व्यक्ति मिठाई की कीमत\n", "कुल लोगों की संख्या = 500 ÷ 20 = 25 लोग\n", "</think>\n", "\n", "दीवाली के त्योहार के लिए राम ने कुल 500 रुपये में मिठाई खरीदी और उसने अपने 4 दोस्तों के साथ बराबर बांटी। यदि हर व्यक्ति को 20 रुपये की मिठाई मिली, तो कुल लोगों की संख्या इस प्रकार निकाली जा सकती है:\n", "\n", "1. सबसे पहले, हमें कुल मिठाई की कीमत और प्रति व्यक्ति मिठाई की कीमत का अनुपात निकालना होगा।\n", "2. कुल मिठाई की कीमत = 500 रुपये\n", "3. प्रति व्यक्ति मिठाई की कीमत = 20 रुपये\n", "4. इसलिए, कुल लोगों की संख्या = 500 ÷ 20 = 25 लोग\n", "\n", "अतः दीवाली के त्योहार पर कुल 25 लोग थे।\n", "\n", "============================================================\n", "\n"]}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-901cef6b40bc4cb78c6c2913848be772', created=1752231954701, model='HelpingAI/Dhanishtha-2.0-preview', choices=[Choice(index=0, message=ChatCompletionMessage(role='assistant', content='<think>\\nThis is a math problem in Hindi about Diwali. <PERSON> bought sweets for 500 rupees and shared them equally with 4 friends. If each person got 20 rupees worth of sweets, I need to find the total number of people. Let me work through this step by step.\\n</think>\\n\\nइस प्रश्न को हल करने के लिए मैं धीरे-धीरे सोचूंगी।\\n\\n<think>\\nमुझे दिया गया है कि राम ने कुल 500 रुपये में मिठाई खरीदी और उसने अपने 4 दोस्तों के साथ बराबर बांटी। हर व्यक्ति को 20 रुपये की मिठाई मिली। मुझे कुल लोगों की संख्या निकालनी है।\\n</think>\\n\\nमैं इस समस्या को हल करने के लिए कुल राशि और प्रति व्यक्ति मिठाई के मूल्य का उपयोग करूंगी।\\n\\n<think>\\nकुल मिठाई की कीमत = 500 रुपये\\nप्रति व्यक्ति मिठाई की कीमत = 20 रुपये\\nइसलिए, कुल लोगों की संख्या = कुल मिठाई की कीमत ÷ प्रति व्यक्ति मिठाई की कीमत\\nकुल लोगों की संख्या = 500 ÷ 20 = 25 लोग\\n</think>\\n\\nदीवाली के त्योहार के लिए राम ने कुल 500 रुपये में मिठाई खरीदी और उसने अपने 4 दोस्तों के साथ बराबर बांटी। यदि हर व्यक्ति को 20 रुपये की मिठाई मिली, तो कुल लोगों की संख्या इस प्रकार निकाली जा सकती है:\\n\\n1. सबसे पहले, हमें कुल मिठाई की कीमत और प्रति व्यक्ति मिठाई की कीमत का अनुपात निकालना होगा।\\n2. कुल मिठाई की कीमत = 500 रुपये\\n3. प्रति व्यक्ति मिठाई की कीमत = 20 रुपये\\n4. इसलिए, कुल लोगों की संख्या = 500 ÷ 20 = 25 लोग\\n\\nअतः दीवाली के त्योहार पर कुल 25 लोग थे।', function_call=None, tool_calls=None), delta=None, finish_reason='stop', logprobs=None)], object='chat.completion', system_fingerprint=None, usage=CompletionUsage(completion_tokens=925, prompt_tokens=157, total_tokens=1082, prompt_tokens_details=None))"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Cultural context in Hindi\n", "multilingual_reasoning(\n", "    \"दीवाली के त्योहार के लिए, राम ने 500 रुपये में मिठाई खरीदी। \"\n", "    \"उसने अपने 4 दोस्तों के साथ बराबर बांटी। \"\n", "    \"अगर हर व्यक्ति को 20 रुपये की मिठाई मिली, तो कुल कितने लोग थे?\",\n", "    \"Hindi (Cultural Context)\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Language Capability Analysis\n", "\n", "Let's analyze the reasoning quality across different languages."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 LOGICAL REASONING ACROSS LANGUAGES:\n", "==================================================\n", "\n", "🌍 English:\n", "Reasoning indicators found: 1\n", "Shows thinking process: Yes\n", "Response excerpt: \n", "This is a logical reasoning question about syllogisms. Let me break it down step by step. We have two premises: (1) All roses are flowers, and (2) So...\n", "------------------------------\n", "\n", "🌍 Spanish:\n", "Reasoning indicators found: 2\n", "Shows thinking process: Yes\n", "Response excerpt: \n", "This is a logical reasoning question in Spanish. Let me translate and analyze it carefully. The question asks: \\\"If all roses are flowers, and some f...\n", "------------------------------\n", "\n", "🌍 French:\n", "Reasoning indicators found: 1\n", "Shows thinking process: Yes\n", "Response excerpt: \n", "Cette question est un problème de logique formelle en français. Je dois analyser si on peut conclure que \\\"certaines roses sont rouges\\\" à partir des...\n", "------------------------------\n", "\n", "🌍 German:\n", "Reasoning indicators found: 2\n", "Shows thinking process: Yes\n", "Response excerpt: \n", "This is a logical reasoning question in German. I need to analyze whether we can conclude that \\\"some roses are red\\\" based on the premises \\\"all ros...\n", "------------------------------\n"]}], "source": ["def analyze_multilingual_capabilities():\n", "    \"\"\"Analyze reasoning capabilities across languages\"\"\"\n", "    \n", "    # Test logical reasoning in multiple languages\n", "    logic_problems = {\n", "        \"English\": \"If all roses are flowers, and some flowers are red, can we conclude that some roses are red?\",\n", "        \"Spanish\": \"Si todas las rosas son flores, y algunas flores son rojas, ¿podemos concluir que algunas rosas son rojas?\",\n", "        \"French\": \"Si toutes les roses sont des fleurs, et que certaines fleurs sont rouges, peut-on conclure que certaines roses sont rouges?\",\n", "        \"German\": \"Wenn alle Rosen Blumen sind und einige Blumen rot sind, können wir schließen, dass einige Rosen rot sind?\"\n", "    }\n", "    \n", "    print(\"🧠 LOGICAL REASONING ACROSS LANGUAGES:\")\n", "    print(\"=\" * 50)\n", "    \n", "    for language, problem in logic_problems.items():\n", "        print(f\"\\n🌍 {language}:\")\n", "        \n", "        response = hai.chat.completions.create(\n", "            model=\"Dhanishtha-2.0-preview\",\n", "            messages=[{\"role\": \"user\", \"content\": problem}],\n", "            hide_think=False,\n", "            temperature=0.3,\n", "            max_tokens=800\n", "        )\n", "        \n", "        content = response.choices[0].message.content\n", "        \n", "        # Check for logical reasoning indicators\n", "        reasoning_indicators = ['therefore', 'because', 'since', 'however', 'but',\n", "                              'por lo tanto', 'porque', 'sin embargo',\n", "                              'donc', 'parce que', 'cependant',\n", "                              'daher', 'weil', 'jedoch']\n", "        \n", "        found_indicators = [ind for ind in reasoning_indicators if ind.lower() in content.lower()]\n", "        \n", "        print(f\"Reasoning indicators found: {len(found_indicators)}\")\n", "        \n", "        # Check for thinking blocks\n", "        has_thinking = \"<think>\" in content\n", "        print(f\"Shows thinking process: {'Yes' if has_thinking else 'No'}\")\n", "        \n", "        # Show brief excerpt\n", "        excerpt = content.replace(\"<think>\", \"\").replace(\"</think>\", \"\")[:150]\n", "        print(f\"Response excerpt: {excerpt}...\")\n", "        print(\"-\" * 30)\n", "\n", "analyze_multilingual_capabilities()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Key Insights About Multilingual Reasoning\n", "\n", "From these examples, we can observe several important characteristics:\n", "\n", "### 🌍 Language Support\n", "- **39+ Languages**: Comprehensive support across major world languages\n", "- **Consistent Quality**: Reasoning quality maintained across languages\n", "- **Cultural Awareness**: Understanding of cultural contexts and references\n", "- **Code-Switching**: Ability to handle mixed-language inputs\n", "\n", "### 🧠 Reasoning Consistency\n", "- **Logical Equivalence**: Same logical steps across languages\n", "- **Mathematical Accuracy**: Consistent numerical results\n", "- **Thinking Process**: Transparent reasoning in native language\n", "- **Error Correction**: Self-correction works in all languages\n", "\n", "### 🎭 Cultural Intelligence\n", "- **Context Sensitivity**: Appropriate cultural references\n", "- **Local Examples**: Using culturally relevant scenarios\n", "- **Communication Style**: Adapting to language-specific conventions\n", "- **Educational Approaches**: Culturally appropriate teaching methods\n", "\n", "## 🚀 Applications for Multilingual Reasoning\n", "\n", "This capability makes Dhanishta 2.0 ideal for:\n", "\n", "- **Global Education**: Multilingual learning platforms\n", "- **International Business**: Cross-cultural problem solving\n", "- **Research Collaboration**: Multi-language academic work\n", "- **Customer Support**: Reasoning in customer's native language\n", "- **Translation Services**: Context-aware translations\n", "- **Cultural Exchange**: Cross-cultural understanding\n", "\n", "## 📚 Next Steps\n", "\n", "- **[../advanced/](../advanced/)** - Advanced multilingual applications\n", "- **[../../examples/applications/](../../examples/applications/)** - Real-world multilingual use cases\n", "- **[../../guides/dhanishta-2.0/](../../guides/dhanishta-2.0/)** - Advanced Dhanishta 2.0 features\n", "\n", "---\n", "\n", "**Break language barriers with intelligent multilingual reasoning! 🌍🧠**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}