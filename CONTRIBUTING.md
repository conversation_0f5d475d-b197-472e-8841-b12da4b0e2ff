# Contributing to HelpingAI Python SDK

Thank you for considering contributing to HelpingAI! We welcome bug reports, feature requests, documentation improvements, and code contributions.

## How to Contribute

1. **Fork the repository** and create your branch from `main`.
2. **Install dependencies** using `pip install -r requirements.txt` (if available).
3. **Write clear, concise commit messages**.
4. **Add or update tests** for your changes.
5. **Ensure all tests pass** before submitting a pull request.
6. **Open a pull request** with a clear description of your changes.

## Code Style
- Follow [PEP 8](https://pep8.org/) for Python code style.
- Use type hints and docstrings for all public functions and classes.
- Keep functions small and focused.

## Running Tests
If a `tests/` directory is present, run tests with:

```bash
python -m unittest discover tests
```

## Reporting Issues
- Use the [GitHub Issues](https://github.com/HelpingAI/HelpingAI-python/issues) page.
- Include steps to reproduce, error messages, and environment details.

## Community
- Be respectful and inclusive. See our [Code of Conduct](CODE_OF_CONDUCT.md).

---
Happy coding!
