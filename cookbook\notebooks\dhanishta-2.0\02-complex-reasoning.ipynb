{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Dhanishta 2.0: Complex Reasoning Examples 🧮\n", "\n", "This notebook demonstrates Dhanishta 2.0's advanced reasoning capabilities through complex problems that require multi-step thinking, analysis, and problem-solving.\n", "\n", "## 🎯 What You'll Learn\n", "- Multi-step mathematical reasoning\n", "- Complex logic puzzles\n", "- Scientific problem solving\n", "- Strategic thinking and planning\n", "- Research and analysis tasks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from HelpingAI import HAI\n", "import json\n", "\n", "# Initialize the client\n", "hai = HAI()\n", "\n", "def complex_reasoning(prompt, temperature=0.3, max_tokens=3000):\n", "    \"\"\"Helper function for complex reasoning tasks\"\"\"\n", "    print(f\"🧠 Complex Problem: {prompt}\")\n", "    print(\"=\" * 80)\n", "    \n", "    response = hai.chat.completions.create(\n", "        model=\"Dhanishtha-2.0-preview\",\n", "        messages=[{\"role\": \"user\", \"content\": prompt}],\n", "        hide_think=False,\n", "        temperature=temperature,\n", "        max_tokens=max_tokens\n", "    )\n", "    \n", "    print(response.choices[0].message.content)\n", "    print(\"\\n\" + \"=\" * 80 + \"\\n\")\n", "    return response\n", "\n", "print(\"🚀 Ready for complex reasoning challenges!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧮 Advanced Mathematical Reasoning\n", "\n", "Let's start with complex mathematical problems that require multiple steps and verification."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Complex algebra problem\n", "complex_reasoning(\n", "    \"A company's profit P (in thousands) is given by P = -2x² + 12x - 10, where x is the number of units sold (in hundreds). \"\n", "    \"Find: 1) The number of units that maximizes profit, 2) The maximum profit, 3) The break-even points. \"\n", "    \"Show all work and verify your answers.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compound interest with multiple scenarios\n", "complex_reasoning(\n", "    \"<PERSON> has $10,000 to invest. She's considering three options: \"\n", "    \"1) 5% annual interest compounded monthly for 10 years \"\n", "    \"2) 4.8% annual interest compounded daily for 10 years \"\n", "    \"3) 5.2% annual interest compounded annually for 10 years. \"\n", "    \"Calculate the final amount for each option and recommend the best choice. \"\n", "    \"Also, determine how much more the best option yields compared to the worst.\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧩 Complex Logic Puzzles\n", "\n", "These puzzles require systematic reasoning and elimination of possibilities."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Einstein's riddle variant\n", "complex_reasoning(\n", "    \"Five houses in a row are painted different colors (red, blue, green, yellow, white). \"\n", "    \"Each house is owned by a person of different nationality (American, British, Canadian, Danish, European). \"\n", "    \"Each owner drinks a different beverage (coffee, tea, milk, juice, water), smokes different cigars (Pall Mall, Dunhill, Blend, Blue Master, Prince), \"\n", "    \"and keeps different pets (dog, bird, cat, horse, fish). \"\n", "    \"\\n\\nClues: \"\n", "    \"1. The British person lives in the red house \"\n", "    \"2. The person with the dog lives next to the person with the cat \"\n", "    \"3. The green house is immediately to the left of the white house \"\n", "    \"4. The Danish person drinks tea \"\n", "    \"5. The person who smokes Pall Mall has a bird \"\n", "    \"6. The owner of the yellow house smokes Dunhill \"\n", "    \"7. The person in the middle house drinks milk \"\n", "    \"8. The American lives in the first house \"\n", "    \"9. The person who smokes <PERSON><PERSON><PERSON> lives next to the cat owner \"\n", "    \"10. The horse owner lives next to the Dunhill smoker \"\n", "    \"\\nWho owns the fish? Solve step by step.\",\n", "    temperature=0.2,\n", "    max_tokens=4000\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# River crossing puzzle\n", "complex_reasoning(\n", "    \"A farmer needs to cross a river with a fox, a chicken, and a bag of grain. \"\n", "    \"The boat can only carry the farmer and one other item at a time. \"\n", "    \"If left alone together, the fox will eat the chicken, and the chicken will eat the grain. \"\n", "    \"How can the farmer get everything across safely? \"\n", "    \"Provide a step-by-step solution and explain why each step is necessary.\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔬 Scientific Problem Solving\n", "\n", "Complex scientific scenarios requiring analysis and reasoning."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Physics problem with multiple concepts\n", "complex_reasoning(\n", "    \"A 2 kg block is placed on a 30° inclined plane with a coefficient of kinetic friction of 0.3. \"\n", "    \"The block is given an initial velocity of 5 m/s up the incline. \"\n", "    \"Calculate: 1) How far up the incline the block travels before stopping, \"\n", "    \"2) How long it takes to stop, 3) The velocity when it returns to the starting point. \"\n", "    \"Use g = 9.8 m/s². Show all forces, equations, and reasoning.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chemistry equilibrium problem\n", "complex_reasoning(\n", "    \"For the reaction: N₂(g) + 3H₂(g) ⇌ 2NH₃(g), Kc = 0.5 at 400°C. \"\n", "    \"Initially, 2.0 mol N₂, 6.0 mol H₂, and 0 mol NH₃ are placed in a 2.0 L container. \"\n", "    \"Calculate: 1) The equilibrium concentrations of all species, \"\n", "    \"2) The percent conversion of N₂, 3) What happens if 1.0 mol NH₃ is added after equilibrium. \"\n", "    \"Show the ICE table and all calculations.\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Strategic Planning and Analysis\n", "\n", "Complex scenarios requiring strategic thinking and multi-factor analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Business strategy problem\n", "complex_reasoning(\n", "    \"TechCorp is considering launching a new product. Market research shows: \"\n", "    \"- Development cost: $2M, Marketing cost: $1M \"\n", "    \"- Projected sales: Year 1: 10,000 units, Year 2: 25,000 units, Year 3: 40,000 units \"\n", "    \"- Price per unit: $150, Cost per unit: $80 \"\n", "    \"- Competitor might launch similar product in Year 2, reducing sales by 30% \"\n", "    \"- Alternative: License technology for $500K/year guaranteed income \"\n", "    \"\\nAnalyze both options considering: NPV (10% discount rate), risk factors, \"\n", "    \"break-even analysis, and strategic implications. Provide a recommendation.\",\n", "    temperature=0.4\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Urban planning challenge\n", "complex_reasoning(\n", "    \"A city needs to design a new transportation system for a growing district. Constraints: \"\n", "    \"- Budget: $50M, Population: 100,000, Area: 25 sq km \"\n", "    \"- Current: 80% car usage, 15% bus, 5% walking/cycling \"\n", "    \"- Goals: Reduce car usage to 50%, increase public transport to 40%, active transport to 10% \"\n", "    \"- Options: Light rail ($30M, serves 60% of area), Bus rapid transit ($15M, serves 80% of area), \"\n", "    \"  Bike lanes ($5M, covers 100% of area), Smart traffic systems ($10M, improves all transport) \"\n", "    \"\\nDesign an optimal combination considering cost-effectiveness, environmental impact, \"\n", "    \"and citizen satisfaction. Justify your choices with quantitative analysis.\",\n", "    temperature=0.5\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 Research and Data Analysis\n", "\n", "Complex analytical tasks requiring systematic investigation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Statistical analysis problem\n", "complex_reasoning(\n", "    \"A pharmaceutical company tested a new drug on 1000 patients. Results: \"\n", "    \"- Treatment group (500): 420 improved, 80 no change \"\n", "    \"- Control group (500): 300 improved, 200 no change \"\n", "    \"- Side effects: Treatment: 15% mild, 3% severe; Control: 5% mild, 1% severe \"\n", "    \"\\nAnalyze: 1) Statistical significance of improvement (use chi-square test), \"\n", "    \"2) Number needed to treat (NNT), 3) Risk-benefit analysis, \"\n", "    \"4) Confidence intervals for improvement rates, 5) Recommendation for FDA approval. \"\n", "    \"Show all calculations and statistical reasoning.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Environmental impact assessment\n", "complex_reasoning(\n", "    \"A proposed solar farm project has the following data: \"\n", "    \"- Area: 1000 hectares, Capacity: 500 MW, Lifespan: 25 years \"\n", "    \"- Construction: 2 years, Cost: $500M, Jobs: 200 during construction, 50 permanent \"\n", "    \"- Environmental: Displaces 500 hectares of farmland, saves 800,000 tons CO₂/year \"\n", "    \"- Economic: Electricity for 150,000 homes, $0.08/kWh revenue \"\n", "    \"- Alternatives: Natural gas plant (same capacity, $300M, 400,000 tons CO₂/year) \"\n", "    \"\\nConduct comprehensive analysis: Economic viability (NPV, IRR), \"\n", "    \"environmental impact (carbon payback, land use), social benefits, \"\n", "    \"and comparison with alternatives. Provide evidence-based recommendation.\",\n", "    temperature=0.4\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎲 Game Theory and Decision Making\n", "\n", "Complex scenarios involving strategic interactions and optimal decision making."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prisoner's dilemma variant\n", "complex_reasoning(\n", "    \"Three companies (A, B, C) must decide whether to invest in R&D (cooperate) or cut prices (defect). \"\n", "    \"Payoff matrix (in millions): \"\n", "    \"- All cooperate: Each gets $10M \"\n", "    \"- Two cooperate, one defects: Defector gets $15M, cooperators get $2M each \"\n", "    \"- One cooperates, two defect: Cooperator gets $0M, defectors get $8M each \"\n", "    \"- All defect: Each gets $5M \"\n", "    \"\\nAnalyze: 1) Nash equilibrium, 2) Pareto optimal outcomes, \"\n", "    \"3) Effect of repeated games, 4) Coalition possibilities, \"\n", "    \"5) Real-world strategies for achieving cooperation. \"\n", "    \"Consider both one-shot and repeated game scenarios.\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧠 Meta-Reasoning: Analyzing the Thinking Process\n", "\n", "Let's examine how Dhanishta 2.0 approaches different types of complex problems."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_reasoning_patterns():\n", "    \"\"\"Analyze how Dhanishta 2.0 approaches different problem types\"\"\"\n", "    \n", "    problems = {\n", "        \"Mathematical\": \"Solve: ∫(x² + 3x + 2)dx from 0 to 2\",\n", "        \"Logical\": \"If all roses are flowers, and some flowers are red, can we conclude that some roses are red?\",\n", "        \"Scientific\": \"Explain why ice floats on water from a molecular perspective\",\n", "        \"Strategic\": \"Design a pricing strategy for a new smartphone in a competitive market\"\n", "    }\n", "    \n", "    for problem_type, problem in problems.items():\n", "        print(f\"\\n🔍 ANALYZING {problem_type.upper()} REASONING:\")\n", "        print(\"-\" * 50)\n", "        \n", "        response = hai.chat.completions.create(\n", "            model=\"Dhanishtha-2.0-preview\",\n", "            messages=[{\"role\": \"user\", \"content\": problem}],\n", "            hide_think=False,\n", "            temperature=0.3,\n", "            max_tokens=1500\n", "        )\n", "        \n", "        content = response.choices[0].message.content\n", "        \n", "        # Extract and analyze thinking patterns\n", "        if \"<think>\" in content:\n", "            thinking = content.split(\"<think>\")[1].split(\"</think>\")[0]\n", "            \n", "            # Count reasoning elements\n", "            steps = thinking.count(\"step\") + thinking.count(\"Step\")\n", "            questions = thinking.count(\"?\") \n", "            verifications = thinking.count(\"check\") + thinking.count(\"verify\")\n", "            \n", "            print(f\"Reasoning steps identified: {steps}\")\n", "            print(f\"Questions asked: {questions}\")\n", "            print(f\"Verification attempts: {verifications}\")\n", "            print(f\"Thinking length: {len(thinking)} characters\")\n", "            \n", "            # Show first 200 characters of thinking\n", "            print(f\"\\nThinking preview: {thinking[:200]}...\")\n", "        \n", "        print(\"\\n\" + \"=\" * 50)\n", "\n", "analyze_reasoning_patterns()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Key Insights from Complex Reasoning\n", "\n", "From these examples, we can observe <PERSON><PERSON><PERSON><PERSON> 2.0's approach to complex problems:\n", "\n", "✅ **Systematic Breakdown**: Complex problems are broken into manageable steps  \n", "✅ **Multi-Phase Analysis**: Different aspects are considered separately then integrated  \n", "✅ **Verification**: Solutions are checked and validated  \n", "✅ **Alternative Consideration**: Multiple approaches and scenarios are evaluated  \n", "✅ **Quantitative Rigor**: Mathematical calculations are shown step-by-step  \n", "✅ **Contextual Awareness**: Real-world constraints and implications are considered  \n", "\n", "## 🚀 Applications for Complex Reasoning\n", "\n", "These capabilities make Dhanishta 2.0 ideal for:\n", "\n", "- **Research and Analysis**: Scientific studies, market research, policy analysis\n", "- **Engineering Design**: System optimization, trade-off analysis\n", "- **Financial Modeling**: Investment analysis, risk assessment\n", "- **Strategic Planning**: Business strategy, resource allocation\n", "- **Educational Content**: Complex problem tutorials, case studies\n", "\n", "## 📚 Next Steps\n", "\n", "- **[03-self-correction.ipynb](03-self-correction.ipynb)** - See how AI fixes its mistakes\n", "- **[04-multilingual.ipynb](04-multilingual.ipynb)** - Reasoning across languages\n", "- **[../advanced/](../advanced/)** - Advanced applications and integrations\n", "\n", "---\n", "\n", "**Master complex problem-solving with <PERSON><PERSON><PERSON><PERSON> 2.0! 🧠✨**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}