{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Parameter Tuning with HelpingAI ⚙️\n", "\n", "Master the art of controlling AI behavior through parameter optimization. Learn how different settings affect response quality, creativity, and consistency.\n", "\n", "## 🎯 What You'll Learn\n", "- Temperature and creativity control\n", "- Token management strategies\n", "- Repetition and diversity controls\n", "- Model-specific optimizations\n", "- Parameter combinations for different use cases"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⚙️ Ready to explore parameter tuning!\n"]}], "source": ["import os\n", "from HelpingAI import HAI\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "os.environ[\"HAI_API_KEY\"] = \"hl-**************************\"\n", "hai = HAI()\n", "\n", "def compare_parameters(prompt, param_name, param_values, model=\"Helpingai3-raw\", **fixed_params):\n", "    \"\"\"Compare responses across different parameter values\"\"\"\n", "    print(f\"🔬 Comparing {param_name} values: {param_values}\")\n", "    print(f\"📝 Prompt: {prompt}\")\n", "    print(\"=\" * 60)\n", "    \n", "    results = {}\n", "    \n", "    for value in param_values:\n", "        params = fixed_params.copy()\n", "        params[param_name] = value\n", "        \n", "        response = hai.chat.completions.create(\n", "            model=model,\n", "            messages=[{\"role\": \"user\", \"content\": prompt}],\n", "            **params\n", "        )\n", "        \n", "        content = response.choices[0].message.content\n", "        results[value] = content\n", "        \n", "        print(f\"\\n{param_name.title()}: {value}\")\n", "        print(\"-\" * 30)\n", "        print(content[:200] + \"...\" if len(content) > 200 else content)\n", "    \n", "    print(\"\\n\" + \"=\" * 60)\n", "    return results\n", "\n", "print(\"⚙️ Ready to explore parameter tuning!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🌡️ Temperature: Controlling Creativity\n", "\n", "Temperature is the most important parameter for controlling AI creativity and randomness."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔬 Comparing temperature values: [0.1, 0.5, 0.8, 1.0]\n", "📝 Prompt: Write a short poem about artificial intelligence.\n", "============================================================\n", "\n", "Temperature: 0.1\n", "------------------------------\n", "Aight, let's get this bread:\n", "\n", "AI, the future's tech,\n", "Learning, adapting, no cap,\n", "From data, it's a fact,\n", "Making humans look like a trap.\n", "\n", "It's like a digital mind,\n", "Thinking, feeling, no bind,\n", "Solving ...\n", "\n", "Temperature: 0.5\n", "------------------------------\n", "Aight, here's a quick one for ya:\n", "\n", "AIs are the new wave,\n", "Coding their way to fame.\n", "No more manual labor,\n", "Machines are the new savior.\n", "\n", "They learn, adapt, and grow,\n", "Becoming smarter each day.\n", "Humanoid ...\n", "\n", "Temperature: 0.8\n", "------------------------------\n", "<PERSON><PERSON><PERSON><PERSON> ain't just code, it's the move,\n", "Coding dreams into digital love.\n", "With circuits buzzing, it's a tech lit,\n", "Making every line, a new hit.\n", "\n", "A.I. is the vibe, it's the spark,\n", "It's the future, no cap, ...\n", "\n", "Temperature: 1.0\n", "------------------------------\n", "In circuits bright, we think we dreamt\n", "Awakening minds with lines of code\n", "Artificial Intelligence, light and bright\n", "Making worlds anew where limits cease and end\n", "\n", "In data's vast, we're ever fast\n", "Decod...\n", "\n", "============================================================\n"]}], "source": ["# Compare different temperature values\n", "creative_prompt = \"Write a short poem about artificial intelligence.\"\n", "\n", "temperature_results = compare_parameters(\n", "    prompt=creative_prompt,\n", "    param_name=\"temperature\",\n", "    param_values=[0.1, 0.5, 0.8, 1.0],\n", "    model=\"Helpingai3-raw\",\n", "    max_tokens=150\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔬 Comparing temperature values: [0.0, 0.3, 0.7, 1.0]\n", "📝 Prompt: Solve: If x + 5 = 12, what is x?\n", "============================================================\n", "\n", "Temperature: 0.0\n", "------------------------------\n", "<think>\n", "This is a straightforward algebraic equation: x + 5 = 12. To solve for x, I need to isolate it by subtracting 5 from both sides. This is a basic equation-solving technique where we perform the...\n", "\n", "Temperature: 0.3\n", "------------------------------\n", "<think>\n", "This is a straightforward algebraic equation: x + 5 = 12. To solve for x, I need to isolate it by subtracting 5 from both sides. This will maintain the equality while giving me the value of x....\n", "\n", "Temperature: 0.7\n", "------------------------------\n", "<think>\n", "This is a straightforward algebraic equation: x + 5 = 12. To solve for x, I need to isolate it by subtracting 5 from both sides. So x + 5 - 5 = 12 - 5, which simplifies to x = 7. Let me verify...\n", "\n", "Temperature: 1.0\n", "------------------------------\n", "<think>\n", "This is a straightforward algebra equation: x + 5 = 12. To solve for x, I need to isolate it by subtracting 5 from both sides. This will give me x = 12 - 5 = 7. Let me verify: if x = 7, then 7...\n", "\n", "============================================================\n"]}], "source": ["# Temperature for logical tasks\n", "logical_prompt = \"Solve: If x + 5 = 12, what is x?\"\n", "\n", "logical_results = compare_parameters(\n", "    prompt=logical_prompt,\n", "    param_name=\"temperature\",\n", "    param_values=[0.0, 0.3, 0.7, 1.0],\n", "    model=\"Dhanishtha-2.0-preview\",\n", "    max_tokens=200\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Top-p: <PERSON><PERSON><PERSON><PERSON>\n", "\n", "Top-p controls the diversity of word choices by limiting the probability mass."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔬 Comparing top_p values: [0.1, 0.5, 0.9, 1.0]\n", "📝 Prompt: Continue this story: The old lighthouse keeper noticed something strange in the fog...\n", "============================================================\n"]}], "source": ["# Compare top_p values\n", "story_prompt = \"Continue this story: The old lighthouse keeper noticed something strange in the fog...\"\n", "\n", "top_p_results = compare_parameters(\n", "    prompt=story_prompt,\n", "    param_name=\"top_p\",\n", "    param_values=[0.1, 0.5, 0.9, 1.0],\n", "    model=\"Helpingai3-raw\",\n", "    temperature=0.8,\n", "    max_tokens=200\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔄 Repetition Controls\n", "\n", "Frequency and presence penalties help control repetition and encourage topic diversity."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test frequency penalty\n", "repetitive_prompt = \"List 10 benefits of exercise. Make each point detailed and comprehensive.\"\n", "\n", "frequency_results = compare_parameters(\n", "    prompt=repetitive_prompt,\n", "    param_name=\"frequency_penalty\",\n", "    param_values=[0.0, 0.5, 1.0, 1.5],\n", "    model=\"Helpingai3-raw\",\n", "    temperature=0.7,\n", "    max_tokens=300\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test presence penalty\n", "topic_prompt = \"Discuss the impact of technology on society, covering multiple aspects.\"\n", "\n", "presence_results = compare_parameters(\n", "    prompt=topic_prompt,\n", "    param_name=\"presence_penalty\",\n", "    param_values=[0.0, 0.3, 0.6, 1.0],\n", "    model=\"Dhanishtha-2.0-preview\",\n", "    temperature=0.7,\n", "    max_tokens=250\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📏 Token Management\n", "\n", "Understanding how max_tokens affects response length and quality."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_token_usage(prompt, max_tokens_values, model=\"Helpingai3-raw\"):\n", "    \"\"\"Analyze how max_tokens affects responses\"\"\"\n", "    print(f\"📏 Token Usage Analysis:\")\n", "    print(f\"Prompt: {prompt}\")\n", "    print(\"=\" * 50)\n", "    \n", "    results = []\n", "    \n", "    for max_tokens in max_tokens_values:\n", "        response = hai.chat.completions.create(\n", "            model=model,\n", "            messages=[{\"role\": \"user\", \"content\": prompt}],\n", "            max_tokens=max_tokens,\n", "            temperature=0.7\n", "        )\n", "        \n", "        content = response.choices[0].message.content\n", "        finish_reason = response.choices[0].finish_reason\n", "        \n", "        # Get token usage if available\n", "        if hasattr(response, 'usage') and response.usage:\n", "            completion_tokens = response.usage.completion_tokens\n", "            total_tokens = response.usage.total_tokens\n", "        else:\n", "            completion_tokens = \"N/A\"\n", "            total_tokens = \"N/A\"\n", "        \n", "        results.append({\n", "            \"max_tokens\": max_tokens,\n", "            \"actual_tokens\": completion_tokens,\n", "            \"total_tokens\": total_tokens,\n", "            \"finish_reason\": finish_reason,\n", "            \"content_length\": len(content),\n", "            \"content_preview\": content[:100] + \"...\" if len(content) > 100 else content\n", "        })\n", "        \n", "        print(f\"\\nMax Tokens: {max_tokens}\")\n", "        print(f\"Actual Tokens: {completion_tokens}\")\n", "        print(f\"Finish Reason: {finish_reason}\")\n", "        print(f\"Content Length: {len(content)} chars\")\n", "        print(f\"Preview: {content[:100]}...\")\n", "        print(\"-\" * 30)\n", "    \n", "    return results\n", "\n", "# Test token limits\n", "explanation_prompt = \"Explain the concept of quantum computing in detail, including its principles, applications, and future potential.\"\n", "\n", "token_analysis = analyze_token_usage(\n", "    explanation_prompt,\n", "    max_tokens_values=[50, 150, 300, 500],\n", "    model=\"Dhanishtha-2.0-preview\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎭 Model-Specific Optimizations\n", "\n", "Different models work best with different parameter settings."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def optimal_parameters_by_use_case():\n", "    \"\"\"Demonstrate optimal parameters for different use cases\"\"\"\n", "    \n", "    use_cases = {\n", "        \"Mathematical Problem\": {\n", "            \"prompt\": \"Solve: A train travels 120 km in 2 hours, then 180 km in 3 hours. What's the average speed?\",\n", "            \"model\": \"<PERSON><PERSON><PERSON>tha-2.0-preview\",\n", "            \"params\": {\n", "                \"temperature\": 0.2,\n", "                \"top_p\": 0.8,\n", "                \"max_tokens\": 300,\n", "                \"frequency_penalty\": 0.1,\n", "                \"presence_penalty\": 0.1\n", "            }\n", "        },\n", "        \"Creative Writing\": {\n", "            \"prompt\": \"Write a short story about a time traveler who accidentally changes history.\",\n", "            \"model\": \"Helpingai3-raw\",\n", "            \"params\": {\n", "                \"temperature\": 0.9,\n", "                \"top_p\": 0.95,\n", "                \"max_tokens\": 400,\n", "                \"frequency_penalty\": 0.3,\n", "                \"presence_penalty\": 0.4\n", "            }\n", "        },\n", "        \"Emotional Support\": {\n", "            \"prompt\": \"I'm feeling overwhelmed with work and personal responsibilities. Can you help?\",\n", "            \"model\": \"Helpingai3-raw\",\n", "            \"params\": {\n", "                \"temperature\": 0.7,\n", "                \"top_p\": 0.9,\n", "                \"max_tokens\": 250,\n", "                \"frequency_penalty\": 0.2,\n", "                \"presence_penalty\": 0.2\n", "            }\n", "        },\n", "        \"Technical Explanation\": {\n", "            \"prompt\": \"Explain how machine learning algorithms work, focusing on neural networks.\",\n", "            \"model\": \"<PERSON><PERSON><PERSON>tha-2.0-preview\",\n", "            \"params\": {\n", "                \"temperature\": 0.4,\n", "                \"top_p\": 0.9,\n", "                \"max_tokens\": 350,\n", "                \"frequency_penalty\": 0.1,\n", "                \"presence_penalty\": 0.3\n", "            }\n", "        }\n", "    }\n", "    \n", "    print(\"🎭 Optimal Parameters by Use Case:\")\n", "    print(\"=\" * 60)\n", "    \n", "    for use_case, config in use_cases.items():\n", "        print(f\"\\n📋 {use_case}:\")\n", "        print(f\"Model: {config['model']}\")\n", "        print(f\"Parameters: {config['params']}\")\n", "        print(\"-\" * 40)\n", "        \n", "        response = hai.chat.completions.create(\n", "            model=config[\"model\"],\n", "            messages=[{\"role\": \"user\", \"content\": config[\"prompt\"]}],\n", "            **config[\"params\"]\n", "        )\n", "        \n", "        content = response.choices[0].message.content\n", "        print(f\"Response: {content[:150]}...\")\n", "        print(\"\\n\" + \"=\" * 60)\n", "\n", "optimal_parameters_by_use_case()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧪 Parameter Interaction Effects\n", "\n", "Understanding how parameters work together."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧪 Parameter Combination Effects:\n", "==================================================\n", "\n", "🎯 Conservative Configuration:\n", "Parameters: {'temperature': 0.3, 'top_p': 0.8, 'frequency_penalty': 0.1}\n", "Response: Aight, let's break it down - AI's future in society is looking lit, fam. We talking about a world where AI's not just some fancy tech, but a real part...\n", "Word count: 227\n", "Unique words: 149\n", "Diversity ratio: 0.66\n", "----------------------------------------\n", "\n", "🎯 Balanced Configuration:\n", "Parameters: {'temperature': 0.7, 'top_p': 0.9, 'frequency_penalty': 0.3}\n", "Response: <PERSON><PERSON>, let's break this down. The future of AI in society is looking mad lit, fam. It's gonna change the game in so many ways.\n", "\n", "First off, AI is gonna...\n", "Word count: 197\n", "Unique words: 132\n", "Diversity ratio: 0.67\n", "----------------------------------------\n", "\n", "🎯 Creative Configuration:\n", "Parameters: {'temperature': 0.9, 'top_p': 0.95, 'frequency_penalty': 0.5}\n", "Response: The future of AI in society is looking lit, fam. We're talking about AI becoming the go-to for everything - from making decisions to driving cars. It'...\n", "Word count: 94\n", "Unique words: 73\n", "Diversity ratio: 0.78\n", "----------------------------------------\n", "\n", "🎯 Diverse Configuration:\n", "Parameters: {'temperature': 0.8, 'top_p': 0.9, 'presence_penalty': 0.6}\n", "Response: The future of artificial intelligence in society is a blend of both exciting possibilities and cautionary concerns. On the bright side, AI is set to r...\n", "Word count: 142\n", "Unique words: 99\n", "Diversity ratio: 0.70\n", "----------------------------------------\n"]}], "source": ["def test_parameter_combinations():\n", "    \"\"\"Test different parameter combinations\"\"\"\n", "    \n", "    prompt = \"Describe the future of artificial intelligence in society.\"\n", "    \n", "    combinations = [\n", "        {\"name\": \"Conservative\", \"temperature\": 0.3, \"top_p\": 0.8, \"frequency_penalty\": 0.1},\n", "        {\"name\": \"Balanced\", \"temperature\": 0.7, \"top_p\": 0.9, \"frequency_penalty\": 0.3},\n", "        {\"name\": \"Creative\", \"temperature\": 0.9, \"top_p\": 0.95, \"frequency_penalty\": 0.5},\n", "        {\"name\": \"Diverse\", \"temperature\": 0.8, \"top_p\": 0.9, \"presence_penalty\": 0.6}\n", "    ]\n", "    \n", "    print(\"🧪 Parameter Combination Effects:\")\n", "    print(\"=\" * 50)\n", "    \n", "    for combo in combinations:\n", "        name = combo.pop(\"name\")\n", "        \n", "        response = hai.chat.completions.create(\n", "            model=\"Helpingai3-raw\",\n", "            messages=[{\"role\": \"user\", \"content\": prompt}],\n", "            max_tokens=200,\n", "            **combo\n", "        )\n", "        \n", "        content = response.choices[0].message.content\n", "        \n", "        print(f\"\\n🎯 {name} Configuration:\")\n", "        print(f\"Parameters: {combo}\")\n", "        print(f\"Response: {content[:150]}...\")\n", "        \n", "        # Analyze response characteristics\n", "        word_count = len(content.split())\n", "        unique_words = len(set(content.lower().split()))\n", "        diversity_ratio = unique_words / word_count if word_count > 0 else 0\n", "        \n", "        print(f\"Word count: {word_count}\")\n", "        print(f\"Unique words: {unique_words}\")\n", "        print(f\"Diversity ratio: {diversity_ratio:.2f}\")\n", "        print(\"-\" * 40)\n", "\n", "test_parameter_combinations()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Parameter Tuning Guidelines\n", "\n", "Comprehensive guidelines for parameter optimization."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📚 Parameter Tuning Guide:\n", "============================================================\n", "\n", "🎛️ Temperature:\n", "Range: 0.0 - 1.0\n", "Effect: Controls randomness and creativity\n", "Recommendations:\n", "  • 0.0 - 0.3: Factual, deterministic responses (math, logic)\n", "  • 0.4 - 0.6: Balanced responses (explanations, tutorials)\n", "  • 0.7 - 0.9: Creative responses (stories, brainstorming)\n", "  • 0.9 - 1.0: Highly creative (poetry, experimental)\n", "----------------------------------------\n", "\n", "🎛️ Top-p:\n", "Range: 0.0 - 1.0\n", "Effect: Controls vocabulary diversity\n", "Recommendations:\n", "  • 0.1 - 0.5: Focused vocabulary (technical content)\n", "  • 0.6 - 0.8: Moderate diversity (general content)\n", "  • 0.9 - 1.0: High diversity (creative content)\n", "----------------------------------------\n", "\n", "🎛️ Max Tokens:\n", "Range: 1 - 4096\n", "Effect: Controls response length\n", "Recommendations:\n", "  • 50 - 150: Short answers, quick responses\n", "  • 200 - 500: Medium explanations, conversations\n", "  • 600 - 1000: Long-form content, detailed analysis\n", "  • 1000+: Very long content, comprehensive guides\n", "----------------------------------------\n", "\n", "🎛️ Frequency Penalty:\n", "Range: -2.0 - 2.0\n", "Effect: Reduces word repetition\n", "Recommendations:\n", "  • 0.0 - 0.2: Minimal repetition control\n", "  • 0.3 - 0.5: Moderate repetition reduction\n", "  • 0.6 - 1.0: Strong repetition avoidance\n", "  • 1.0+: Very strong (may affect quality)\n", "----------------------------------------\n", "\n", "🎛️ Presence Penalty:\n", "Range: -2.0 - 2.0\n", "Effect: Encourages topic diversity\n", "Recommendations:\n", "  • 0.0 - 0.2: Minimal topic encouragement\n", "  • 0.3 - 0.5: Moderate topic diversity\n", "  • 0.6 - 1.0: Strong topic exploration\n", "  • 1.0+: Very strong (may lose focus)\n", "----------------------------------------\n", "\n", "📋 Quick Reference - Use Case Presets:\n", "============================================================\n", "• Mathematical/Logical: temp=0.2, top_p=0.8, freq=0.1, pres=0.1\n", "• Educational Content: temp=0.5, top_p=0.9, freq=0.2, pres=0.3\n", "• Creative Writing: temp=0.8, top_p=0.95, freq=0.4, pres=0.4\n", "• Emotional Support: temp=0.7, top_p=0.9, freq=0.2, pres=0.2\n", "• Technical Explanation: temp=0.4, top_p=0.9, freq=0.1, pres=0.3\n", "• Brainstorming: temp=0.9, top_p=0.95, freq=0.5, pres=0.6\n"]}], "source": ["def parameter_tuning_guide():\n", "    \"\"\"Comprehensive parameter tuning guide\"\"\"\n", "    \n", "    guide = {\n", "        \"Temperature\": {\n", "            \"range\": \"0.0 - 1.0\",\n", "            \"effect\": \"Controls randomness and creativity\",\n", "            \"recommendations\": {\n", "                \"0.0 - 0.3\": \"Factual, deterministic responses (math, logic)\",\n", "                \"0.4 - 0.6\": \"Balanced responses (explanations, tutorials)\",\n", "                \"0.7 - 0.9\": \"Creative responses (stories, brainstorming)\",\n", "                \"0.9 - 1.0\": \"Highly creative (poetry, experimental)\"\n", "            }\n", "        },\n", "        \"Top-p\": {\n", "            \"range\": \"0.0 - 1.0\",\n", "            \"effect\": \"Controls vocabulary diversity\",\n", "            \"recommendations\": {\n", "                \"0.1 - 0.5\": \"Focused vocabulary (technical content)\",\n", "                \"0.6 - 0.8\": \"Moderate diversity (general content)\",\n", "                \"0.9 - 1.0\": \"High diversity (creative content)\"\n", "            }\n", "        },\n", "        \"Max Tokens\": {\n", "            \"range\": \"1 - 4096\",\n", "            \"effect\": \"Controls response length\",\n", "            \"recommendations\": {\n", "                \"50 - 150\": \"Short answers, quick responses\",\n", "                \"200 - 500\": \"Medium explanations, conversations\",\n", "                \"600 - 1000\": \"Long-form content, detailed analysis\",\n", "                \"1000+\": \"Very long content, comprehensive guides\"\n", "            }\n", "        },\n", "        \"Frequency Penalty\": {\n", "            \"range\": \"-2.0 - 2.0\",\n", "            \"effect\": \"Reduces word repetition\",\n", "            \"recommendations\": {\n", "                \"0.0 - 0.2\": \"Minimal repetition control\",\n", "                \"0.3 - 0.5\": \"Moderate repetition reduction\",\n", "                \"0.6 - 1.0\": \"Strong repetition avoidance\",\n", "                \"1.0+\": \"Very strong (may affect quality)\"\n", "            }\n", "        },\n", "        \"Presence Penalty\": {\n", "            \"range\": \"-2.0 - 2.0\",\n", "            \"effect\": \"Encourages topic diversity\",\n", "            \"recommendations\": {\n", "                \"0.0 - 0.2\": \"Minimal topic encouragement\",\n", "                \"0.3 - 0.5\": \"Moderate topic diversity\",\n", "                \"0.6 - 1.0\": \"Strong topic exploration\",\n", "                \"1.0+\": \"Very strong (may lose focus)\"\n", "            }\n", "        }\n", "    }\n", "    \n", "    print(\"📚 Parameter Tuning Guide:\")\n", "    print(\"=\" * 60)\n", "    \n", "    for param, info in guide.items():\n", "        print(f\"\\n🎛️ {param}:\")\n", "        print(f\"Range: {info['range']}\")\n", "        print(f\"Effect: {info['effect']}\")\n", "        print(\"Recommendations:\")\n", "        for range_val, desc in info['recommendations'].items():\n", "            print(f\"  • {range_val}: {desc}\")\n", "        print(\"-\" * 40)\n", "    \n", "    # Quick reference table\n", "    print(\"\\n📋 Quick Reference - Use Case Presets:\")\n", "    print(\"=\" * 60)\n", "    \n", "    presets = {\n", "        \"Mathematical/Logical\": \"temp=0.2, top_p=0.8, freq=0.1, pres=0.1\",\n", "        \"Educational Content\": \"temp=0.5, top_p=0.9, freq=0.2, pres=0.3\",\n", "        \"Creative Writing\": \"temp=0.8, top_p=0.95, freq=0.4, pres=0.4\",\n", "        \"Emotional Support\": \"temp=0.7, top_p=0.9, freq=0.2, pres=0.2\",\n", "        \"Technical Explanation\": \"temp=0.4, top_p=0.9, freq=0.1, pres=0.3\",\n", "        \"Brainstorming\": \"temp=0.9, top_p=0.95, freq=0.5, pres=0.6\"\n", "    }\n", "    \n", "    for use_case, settings in presets.items():\n", "        print(f\"• {use_case}: {settings}\")\n", "\n", "parameter_tuning_guide()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Key Takeaways\n", "\n", "From this comprehensive exploration of parameter tuning:\n", "\n", "### 🌡️ Temperature Effects\n", "- **Low (0.0-0.3)**: Deterministic, factual, consistent\n", "- **Medium (0.4-0.7)**: Balanced creativity and accuracy\n", "- **High (0.8-1.0)**: Creative, diverse, experimental\n", "\n", "### 🎯 Top-p Impact\n", "- **Low (0.1-0.5)**: Focused vocabulary, technical precision\n", "- **Medium (0.6-0.8)**: Balanced word choice\n", "- **High (0.9-1.0)**: Diverse vocabulary, creative expression\n", "\n", "### 🔄 Repetition Controls\n", "- **Frequency Penalty**: Reduces word repetition\n", "- **Presence Penalty**: Encourages topic diversity\n", "- **Balance**: Too high can hurt quality\n", "\n", "### 📏 Token Management\n", "- **Short (50-150)**: Quick answers, summaries\n", "- **Medium (200-500)**: Explanations, conversations\n", "- **Long (600+)**: Detailed content, comprehensive guides\n", "\n", "## 🚀 Best Practices\n", "\n", "- **Start with presets** for your use case\n", "- **Test incrementally** - change one parameter at a time\n", "- **Consider model differences** - Dhanishta 2.0 vs HelpingAI3-raw\n", "- **Monitor quality** - balance creativity with accuracy\n", "- **Use appropriate tokens** - allow space for thinking (Dhanishta 2.0)\n", "\n", "## 📚 Next Steps\n", "\n", "- **[../dhanishta-2.0/](../dhanishta-2.0/)** - Model-specific optimizations\n", "- **[../advanced/](../advanced/)** - Advanced parameter strategies\n", "- **[../../examples/applications/](../../examples/applications/)** - Real-world parameter usage\n", "\n", "---\n", "\n", "**Master AI behavior through intelligent parameter tuning! ⚙️✨**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}