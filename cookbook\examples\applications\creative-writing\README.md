# Creative Writing Assistant with HelpingAI ✍️

A powerful creative writing companion that combines HelpingAI3-raw's emotional intelligence with Dhanishta 2.0's structured reasoning to help writers craft compelling stories, develop characters, and overcome creative blocks.

## 🌟 Features

- **Story Generation**: Create engaging narratives across genres
- **Character Development**: Build complex, believable characters
- **Plot Structure**: Organize stories with proven narrative frameworks
- **Style Analysis**: Improve writing style and voice
- **Creative Prompts**: Overcome writer's block with inspiring prompts
- **Collaborative Writing**: Co-create stories with AI assistance

## 🚀 Quick Start

### Installation

```bash
pip install HelpingAI streamlit plotly wordcloud textstat
```

### Basic Usage

```python
from creative_writing import CreativeWritingAssistant

# Initialize the assistant
writer = CreativeWritingAssistant()

# Generate a story idea
idea = writer.generate_story_idea(
    genre="science fiction",
    theme="artificial intelligence",
    length="short story"
)

# Develop a character
character = writer.develop_character(
    role="protagonist",
    archetype="reluctant hero",
    background="AI researcher"
)

# Write opening paragraph
opening = writer.write_opening(
    story_idea=idea,
    character=character,
    style="mysterious"
)

print(opening)
```

### Web Interface

```bash
streamlit run writing_app.py
```

## 📁 Files

- `creative_writing.py` - Core writing assistant
- `writing_app.py` - Streamlit web interface
- `story_structures.py` - Narrative frameworks
- `character_archetypes.py` - Character development tools
- `style_analyzer.py` - Writing style analysis
- `prompt_generator.py` - Creative prompt system

## 🎭 Writing Modes

### 📖 Story Generation
```python
# Generate complete short story
story = writer.generate_story(
    prompt="A time traveler discovers they can't return home",
    genre="science fiction",
    length=1500,  # words
    style="literary"
)
```

### 👥 Character Development
```python
# Create detailed character profile
character = writer.create_character(
    name="Elena Rodriguez",
    age=32,
    occupation="quantum physicist",
    personality_traits=["curious", "determined", "empathetic"],
    backstory="Lost her mentor in a lab accident",
    goals=["prove her theory", "honor her mentor's memory"],
    conflicts=["self-doubt", "ethical dilemmas"]
)
```

### 🗺️ Plot Planning
```python
# Structure story using Hero's Journey
plot = writer.plan_plot(
    structure="hero_journey",
    protagonist=character,
    central_conflict="Must choose between scientific discovery and human safety",
    theme="responsibility of knowledge"
)
```

## 🎨 Creative Features

### Emotional Resonance with HelpingAI3-raw
```python
def enhance_emotional_depth(scene, emotion):
    """Add emotional depth to scenes"""
    
    response = hai.chat.completions.create(
        model="Helpingai3-raw",
        messages=[
            {
                "role": "system",
                "content": "You are a master of emotional storytelling. Help writers create scenes that resonate deeply with readers."
            },
            {
                "role": "user",
                "content": f"Enhance this scene to convey {emotion}: {scene}"
            }
        ],
        temperature=0.8
    )
    
    return response.choices[0].message.content
```

### Structured Plotting with Dhanishta 2.0
```python
def analyze_plot_structure(story_outline):
    """Analyze and improve plot structure"""
    
    response = hai.chat.completions.create(
        model="Dhanishtha-2.0-preview",
        messages=[
            {
                "role": "user",
                "content": f"Analyze this story structure and suggest improvements: {story_outline}"
            }
        ],
        hide_think=False,  # Show reasoning process
        temperature=0.4
    )
    
    return response.choices[0].message.content
```

## 📚 Genre Specializations

### 🚀 Science Fiction
- **Worldbuilding**: Create consistent future societies
- **Technology**: Develop plausible scientific concepts
- **Themes**: Explore humanity's relationship with technology

### 💕 Romance
- **Character Chemistry**: Build believable relationships
- **Emotional Arcs**: Craft satisfying romantic journeys
- **Dialogue**: Write authentic romantic conversations

### 🕵️ Mystery/Thriller
- **Plot Twists**: Create surprising yet logical reveals
- **Pacing**: Build and maintain suspense
- **Clues**: Plant evidence that's fair but challenging

### 🏰 Fantasy
- **Magic Systems**: Design consistent magical rules
- **Mythology**: Create rich cultural backgrounds
- **Quests**: Structure epic adventures

## 🛠️ Writing Tools

### Style Analysis
```python
def analyze_writing_style(text):
    """Analyze writing style and provide feedback"""
    
    import textstat
    
    analysis = {
        "readability": textstat.flesch_reading_ease(text),
        "grade_level": textstat.flesch_kincaid_grade(text),
        "sentence_count": textstat.sentence_count(text),
        "avg_sentence_length": textstat.avg_sentence_length(text),
        "word_count": textstat.lexicon_count(text),
        "syllable_count": textstat.syllable_count(text)
    }
    
    # Get AI feedback on style
    style_feedback = hai.chat.completions.create(
        model="Helpingai3-raw",
        messages=[
            {
                "role": "user",
                "content": f"Analyze the writing style of this text and provide constructive feedback: {text[:500]}..."
            }
        ],
        temperature=0.6
    )
    
    analysis["ai_feedback"] = style_feedback.choices[0].message.content
    return analysis
```

### Character Consistency Checker
```python
def check_character_consistency(character_profile, story_text):
    """Ensure character actions align with their profile"""
    
    response = hai.chat.completions.create(
        model="Dhanishtha-2.0-preview",
        messages=[
            {
                "role": "user",
                "content": f"Character Profile: {character_profile}\n\n"
                          f"Story Text: {story_text}\n\n"
                          f"Check if the character's actions and dialogue are consistent with their profile. "
                          f"Point out any inconsistencies and suggest improvements."
            }
        ],
        hide_think=False,
        temperature=0.3
    )
    
    return response.choices[0].message.content
```

### Dialogue Enhancement
```python
def improve_dialogue(dialogue, character1, character2):
    """Make dialogue more natural and character-specific"""
    
    response = hai.chat.completions.create(
        model="Helpingai3-raw",
        messages=[
            {
                "role": "system",
                "content": "You are an expert at writing natural, character-driven dialogue."
            },
            {
                "role": "user",
                "content": f"Improve this dialogue between {character1} and {character2}:\n\n{dialogue}\n\n"
                          f"Make it more natural, character-specific, and emotionally engaging."
            }
        ],
        temperature=0.7
    )
    
    return response.choices[0].message.content
```

## 🎯 Creative Prompts System

### Daily Writing Prompts
```python
def generate_daily_prompt(genre=None, difficulty="medium"):
    """Generate inspiring daily writing prompts"""
    
    prompt_types = [
        "character_driven",
        "situation_based",
        "dialogue_starter",
        "world_building",
        "theme_exploration"
    ]
    
    prompt_type = random.choice(prompt_types)
    
    response = hai.chat.completions.create(
        model="Helpingai3-raw",
        messages=[
            {
                "role": "user",
                "content": f"Generate a {difficulty} difficulty {prompt_type} writing prompt"
                          f"{f' for {genre}' if genre else ''}. "
                          f"Make it inspiring and specific enough to spark creativity."
            }
        ],
        temperature=0.9
    )
    
    return {
        "type": prompt_type,
        "prompt": response.choices[0].message.content,
        "genre": genre,
        "difficulty": difficulty
    }
```

### Writer's Block Breakers
```python
def overcome_writers_block(current_scene, stuck_point):
    """Help overcome specific writing obstacles"""
    
    strategies = [
        "change_perspective",
        "add_conflict",
        "explore_backstory",
        "introduce_new_character",
        "shift_setting"
    ]
    
    suggestions = []
    
    for strategy in strategies:
        response = hai.chat.completions.create(
            model="Dhanishtha-2.0-preview",
            messages=[
                {
                    "role": "user",
                    "content": f"I'm stuck at this point in my story: {stuck_point}\n\n"
                              f"Current scene: {current_scene}\n\n"
                              f"Suggest how to use the '{strategy}' technique to move forward."
                }
            ],
            temperature=0.8
        )
        
        suggestions.append({
            "strategy": strategy,
            "suggestion": response.choices[0].message.content
        })
    
    return suggestions
```

## 📊 Writing Analytics

### Progress Tracking
```python
class WritingTracker:
    def __init__(self):
        self.sessions = []
        self.word_counts = []
        self.goals = {}
    
    def log_session(self, words_written, time_spent, mood="neutral"):
        session = {
            "date": datetime.now(),
            "words": words_written,
            "time": time_spent,
            "mood": mood,
            "words_per_minute": words_written / time_spent if time_spent > 0 else 0
        }
        self.sessions.append(session)
    
    def get_statistics(self):
        if not self.sessions:
            return {"message": "No writing sessions recorded"}
        
        total_words = sum(session["words"] for session in self.sessions)
        total_time = sum(session["time"] for session in self.sessions)
        avg_wpm = total_words / total_time if total_time > 0 else 0
        
        return {
            "total_sessions": len(self.sessions),
            "total_words": total_words,
            "total_time": total_time,
            "average_wpm": round(avg_wpm, 2),
            "most_productive_day": max(self.sessions, key=lambda x: x["words"])["date"].strftime("%Y-%m-%d")
        }
```

### Story Structure Analysis
```python
def analyze_story_structure(story_text):
    """Analyze story structure and pacing"""
    
    response = hai.chat.completions.create(
        model="Dhanishtha-2.0-preview",
        messages=[
            {
                "role": "user",
                "content": f"Analyze the structure and pacing of this story. "
                          f"Identify the key plot points, character development, "
                          f"and suggest improvements:\n\n{story_text}"
            }
        ],
        hide_think=False,
        temperature=0.4
    )
    
    return response.choices[0].message.content
```

## 🎨 Advanced Features

### Collaborative Writing
```python
def collaborative_writing_session(writers, story_prompt):
    """Facilitate collaborative writing between multiple writers"""
    
    session = {
        "prompt": story_prompt,
        "writers": writers,
        "contributions": [],
        "current_turn": 0
    }
    
    # AI moderates the collaboration
    moderation = hai.chat.completions.create(
        model="Helpingai3-raw",
        messages=[
            {
                "role": "user",
                "content": f"You're moderating a collaborative writing session. "
                          f"Prompt: {story_prompt}\n"
                          f"Writers: {', '.join(writers)}\n"
                          f"Provide guidance for how to structure this collaboration."
            }
        ],
        temperature=0.6
    )
    
    session["guidance"] = moderation.choices[0].message.content
    return session
```

### Genre Blending
```python
def blend_genres(primary_genre, secondary_genre, story_elements):
    """Create unique stories by blending genres"""
    
    response = hai.chat.completions.create(
        model="Helpingai3-raw",
        messages=[
            {
                "role": "user",
                "content": f"Create a story concept that blends {primary_genre} with {secondary_genre}. "
                          f"Include these elements: {', '.join(story_elements)}. "
                          f"Make it unique and compelling."
            }
        ],
        temperature=0.8
    )
    
    return response.choices[0].message.content
```

### Voice Consistency
```python
def maintain_narrative_voice(existing_text, new_paragraph):
    """Ensure new content matches established narrative voice"""
    
    response = hai.chat.completions.create(
        model="Dhanishtha-2.0-preview",
        messages=[
            {
                "role": "user",
                "content": f"Existing text (for voice reference): {existing_text[:500]}...\n\n"
                          f"New paragraph to check: {new_paragraph}\n\n"
                          f"Does the new paragraph match the narrative voice? "
                          f"If not, rewrite it to match."
            }
        ],
        temperature=0.4
    )
    
    return response.choices[0].message.content
```

## 🎯 Getting Started

1. **Install dependencies**: `pip install -r requirements.txt`
2. **Set up API key**: `export HAI_API_KEY=your-key-here`
3. **Run the web app**: `streamlit run writing_app.py`
4. **Choose your project**: Novel, short story, or poetry
5. **Start creating**: Use prompts, develop characters, write!

## 📚 Example Workflows

### Short Story Creation
1. Generate story idea with theme and genre
2. Develop main character with detailed profile
3. Plan plot structure using chosen framework
4. Write first draft with AI assistance
5. Analyze and improve style and pacing
6. Polish dialogue and character consistency

### Novel Planning
1. Develop multiple character profiles
2. Create detailed world and setting
3. Plan multi-chapter structure
4. Write chapter outlines
5. Track character arcs and subplots
6. Maintain consistency across chapters

---

**Unleash your creativity with intelligent writing assistance! ✍️✨**
