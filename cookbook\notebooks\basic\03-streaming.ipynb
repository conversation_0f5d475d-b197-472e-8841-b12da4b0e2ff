{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Streaming Responses with HelpingAI 🌊\n", "\n", "Learn how to implement real-time streaming responses for better user experience, especially useful for long-form content generation and interactive applications.\n", "\n", "## 🎯 What You'll Learn\n", "- Basic streaming implementation\n", "- Handling streaming data\n", "- Building real-time interfaces\n", "- Error handling in streams\n", "- Advanced streaming patterns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌊 Ready to explore streaming responses!\n"]}], "source": ["import os\n", "import time\n", "import sys\n", "from HelpingAI import HAI\n", "from IPython.display import display, clear_output\n", "import threading\n", "\n", "os.environ[\"HAI_API_KEY\"] = \"hl-*******************\"\n", "hai = HAI()\n", "\n", "print(\"🌊 Ready to explore streaming responses!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 Basic Streaming\n", "\n", "Let's start with a simple streaming example."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌊 Basic Streaming Example:\n", "========================================\n", "AI Response (streaming): Ai<PERSON>, fam. So this robot, <PERSON><PERSON>, was straight up tired of being a data whiz. It was like, \"Nah, I wanna paint. I wanna make art.\" So it starts in some corner, just using pixels and lines. At first, it's like a big blob, but it keeps trying. It's like, \"I got this.\"\n", "\n", "Then, it starts learning, watching all the art bots out there. It's like, \"Wait, you can do this?\" So R3 starts mixing colors, playing with shades. It's like a kid in a candy store with its paint palette.\n", "\n", "But then, it starts getting mad. Like, \"Where's my style?\" It's like, \"Yo, I'm not just copying. I'm adding my own vibe.\" So it starts putting its own twist on things. Like, it's like it's saying, \"I'm in the game now.\"\n", "\n", "And you know what? People are liking it. They're coming up to R3, saying, \"<PERSON><PERSON>, this is lit.\" And R3's like, \"I told you I was gonna paint.\"\n", "\n", "So there you have it, fam. R3's journey from pixels to a whole new level of art. It's like, \"I may be a robot, but I can still get my paint on.\" 🖌️👾\n", "\n", "========================================\n", "✅ Streaming complete! Total characters: 982\n"]}], "source": ["def basic_streaming_example():\n", "    \"\"\"Basic streaming response example\"\"\"\n", "    print(\"🌊 Basic Streaming Example:\")\n", "    print(\"=\" * 40)\n", "    print(\"AI Response (streaming): \", end=\"\")\n", "    \n", "    # Create streaming response\n", "    stream = hai.chat.completions.create(\n", "        model=\"Helpingai3-raw\",\n", "        messages=[\n", "            {\"role\": \"user\", \"content\": \"Tell me a short story about a robot learning to paint.\"}\n", "        ],\n", "        stream=True,\n", "        temperature=0.8,\n", "        max_tokens=500\n", "    )\n", "    \n", "    # Process the stream\n", "    full_response = \"\"\n", "    for chunk in stream:\n", "        if chunk.choices[0].delta.content:\n", "            content = chunk.choices[0].delta.content\n", "            print(content, end=\"\", flush=True)\n", "            full_response += content\n", "            time.sleep(0.02)  # Small delay to simulate real-time typing\n", "    \n", "    print(\"\\n\\n\" + \"=\" * 40)\n", "    print(f\"✅ Streaming complete! Total characters: {len(full_response)}\")\n", "    return full_response\n", "\n", "# Run basic streaming example\n", "story = basic_streaming_example()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧠 Streaming with Dhanishta 2.0 Thinking\n", "\n", "See how thinking processes stream in real-time."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 Streaming with Thinking Process:\n", "==================================================\n", "\n", "🤔 [THINKING] \u001b[94m\u001b[0m\u001b[94m\n", "\u001b[0m\u001b[94mLet\u001b[0m\u001b[94m me\u001b[0m\u001b[94m solve\u001b[0m\u001b[94m this\u001b[0m\u001b[94m pizza\u001b[0m\u001b[94m problem\u001b[0m\u001b[94m step\u001b[0m\u001b[94m by\u001b[0m\u001b[94m step\u001b[0m\u001b[94m.\u001b[0m\u001b[94m We\u001b[0m\u001b[94m have\u001b[0m\u001b[94m a\u001b[0m\u001b[94m pizza\u001b[0m\u001b[94m cut\u001b[0m\u001b[94m into\u001b[0m\u001b[94m \u001b[0m\u001b[94m8\u001b[0m\u001b[94m equal\u001b[0m\u001b[94m slices\u001b[0m\u001b[94m.\u001b[0m\u001b[94m Three\u001b[0m\u001b[94m people\u001b[0m\u001b[94m each\u001b[0m\u001b[94m eat\u001b[0m\u001b[94m \u001b[0m\u001b[94m2\u001b[0m\u001b[94m slices\u001b[0m\u001b[94m,\u001b[0m\u001b[94m so\u001b[0m\u001b[94m that\u001b[0m\u001b[94m's\u001b[0m\u001b[94m \u001b[0m\u001b[94m3\u001b[0m\u001b[94m ×\u001b[0m\u001b[94m \u001b[0m\u001b[94m2\u001b[0m\u001b[94m =\u001b[0m\u001b[94m \u001b[0m\u001b[94m6\u001b[0m\u001b[94m slices\u001b[0m\u001b[94m total\u001b[0m\u001b[94m consumed\u001b[0m\u001b[94m.\u001b[0m\u001b[94m Out\u001b[0m\u001b[94m of\u001b[0m\u001b[94m the\u001b[0m\u001b[94m original\u001b[0m\u001b[94m \u001b[0m\u001b[94m8\u001b[0m\u001b[94m slices\u001b[0m\u001b[94m,\u001b[0m\u001b[94m we\u001b[0m\u001b[94m now\u001b[0m\u001b[94m have\u001b[0m\u001b[94m \u001b[0m\u001b[94m8\u001b[0m\u001b[94m -\u001b[0m\u001b[94m \u001b[0m\u001b[94m6\u001b[0m\u001b[94m =\u001b[0m\u001b[94m \u001b[0m\u001b[94m2\u001b[0m\u001b[94m slices\u001b[0m\u001b[94m remaining\u001b[0m\u001b[94m.\u001b[0m\u001b[94m To\u001b[0m\u001b[94m express\u001b[0m\u001b[94m this\u001b[0m\u001b[94m as\u001b[0m\u001b[94m a\u001b[0m\u001b[94m fraction\u001b[0m\u001b[94m of\u001b[0m\u001b[94m the\u001b[0m\u001b[94m whole\u001b[0m\u001b[94m pizza\u001b[0m\u001b[94m,\u001b[0m\u001b[94m it\u001b[0m\u001b[94m would\u001b[0m\u001b[94m be\u001b[0m\u001b[94m \u001b[0m\u001b[94m2\u001b[0m\u001b[94m/\u001b[0m\u001b[94m8\u001b[0m\u001b[94m,\u001b[0m\u001b[94m which\u001b[0m\u001b[94m can\u001b[0m\u001b[94m be\u001b[0m\u001b[94m simplified\u001b[0m\u001b[94m to\u001b[0m\u001b[94m \u001b[0m\u001b[94m1\u001b[0m\u001b[94m/\u001b[0m\u001b[94m4\u001b[0m\u001b[94m.\n", "\u001b[0m\n", "\n", "💡 [SOLUTION] \n", "\n", "Let me work through this pizza problem carefully:\n", "\n", "First, we have a pizza that's been cut into 8 equal slices.\n", "\n", "Three people each eat 2 slices, so in total, they consume 3 × 2 = 6 slices.\n", "\n", "This means we started with 8 slices and 6 were eaten, leaving us with 8 - 6 = 2 slices remaining.\n", "\n", "To express this as a fraction of the whole pizza, we take the number of slices left (2) and divide by the total number of slices (8).\n", "\n", "So the fraction of pizza left is 2/8.\n", "\n", "\n", "🤔 [THINKING] \u001b[94m\u001b[0m\u001b[94m\n", "\u001b[0m\u001b[94mI\u001b[0m\u001b[94m should\u001b[0m\u001b[94m simplify\u001b[0m\u001b[94m this\u001b[0m\u001b[94m fraction\u001b[0m\u001b[94m further\u001b[0m\u001b[94m.\u001b[0m\u001b[94m Both\u001b[0m\u001b[94m \u001b[0m\u001b[94m2\u001b[0m\u001b[94m and\u001b[0m\u001b[94m \u001b[0m\u001b[94m8\u001b[0m\u001b[94m are\u001b[0m\u001b[94m divisible\u001b[0m\u001b[94m by\u001b[0m\u001b[94m \u001b[0m\u001b[94m2\u001b[0m\u001b[94m,\u001b[0m\u001b[94m so\u001b[0m\u001b[94m \u001b[0m\u001b[94m2\u001b[0m\u001b[94m/\u001b[0m\u001b[94m8\u001b[0m\u001b[94m =\u001b[0m\u001b[94m \u001b[0m\u001b[94m1\u001b[0m\u001b[94m/\u001b[0m\u001b[94m4\u001b[0m\u001b[94m.\u001b[0m\u001b[94m This\u001b[0m\u001b[94m is\u001b[0m\u001b[94m our\u001b[0m\u001b[94m final\u001b[0m\u001b[94m answer\u001b[0m\u001b[94m.\n", "\u001b[0m\n", "\n", "💡 [SOLUTION] \n", "\n", "We can simplify this fraction by dividing both the numerator and denominator by their greatest common divisor, which is 2:\n", "\n", "2 ÷ 2 = 1\n", "8 ÷ 2 = 4\n", "\n", "Therefore, 2/8 simplifies to 1/4.\n", "\n", "So after three people each enjoy 2 slices of pizza, one-fourth of the pizza remains.\n", "\n", "==================================================\n"]}], "source": ["def streaming_with_thinking():\n", "    \"\"\"Stream Dhanishta 2.0 responses with thinking process\"\"\"\n", "    print(\"🧠 Streaming with Thinking Process:\")\n", "    print(\"=\" * 50)\n", "    \n", "    stream = hai.chat.completions.create(\n", "        model=\"Dhanishtha-2.0-preview\",\n", "        messages=[\n", "            {\n", "                \"role\": \"user\", \n", "                \"content\": \"Solve this step by step: If a pizza is cut into 8 equal slices and 3 people eat 2 slices each, what fraction of the pizza is left?\"\n", "            }\n", "        ],\n", "        stream=True,\n", "        hide_think=False,  # Show thinking process\n", "        temperature=0.3\n", "    )\n", "    \n", "    full_response = \"\"\n", "    in_thinking = False\n", "    \n", "    for chunk in stream:\n", "        if chunk.choices[0].delta.content:\n", "            content = chunk.choices[0].delta.content\n", "            \n", "            # Detect thinking blocks\n", "            if \"<think>\" in content:\n", "                in_thinking = True\n", "                print(\"\\n🤔 [THINKING] \", end=\"\")\n", "                content = content.replace(\"<think>\", \"\")\n", "            \n", "            if \"</think>\" in content:\n", "                in_thinking = False\n", "                content = content.replace(\"</think>\", \"\")\n", "                print(\"\\n\\n💡 [SOLUTION] \", end=\"\")\n", "            \n", "            # Color coding for different sections\n", "            if in_thinking:\n", "                print(f\"\\033[94m{content}\\033[0m\", end=\"\", flush=True)  # Blue for thinking\n", "            else:\n", "                print(content, end=\"\", flush=True)  # Normal for solution\n", "            \n", "            full_response += content\n", "            time.sleep(0.03)\n", "    \n", "    print(\"\\n\\n\" + \"=\" * 50)\n", "    return full_response\n", "\n", "# Run thinking stream example\n", "math_solution = streaming_with_thinking()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎮 Interactive Streaming Interface\n", "\n", "Build an interactive streaming chat interface."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💬 Interactive Streaming Chat Demo:\n", "==================================================\n", "👤 You: Hello! Can you help me understand what makes a good story?\n", "🤖 AI: ...<PERSON><PERSON>, listen up! A good story's got some key ingredients, ya feel me? \n", "\n", "1. 🔥 Plot: Gotta have a story that keeps you on your toes. Something that's gonna make you go, \"Whoaa, what's good?\" \n", "\n", "2. 🧠 Characters: You need people you can vibe with, who're gonna make you laugh, cry, or just be like, \"<PERSON><PERSON><PERSON>, that's wild.\"\n", "\n", "3. 💬 Dialogue: The way they talk to each other matters. It's like, do they sound real or fake? \n", "\n", "So, if you got those, you're cooking with gas. Just make sure you mix it up, keep it spicy, and don't leave anyone hanging. That's the recipe for a story that slaps! 😎\n", "\n", "------------------------------\n", "👤 You: That's helpful! Can you give me an example of a compelling character?\n", "🤖 AI: ...Yeah, no problem! A compelling character's like a legend in the making. They got that spark, that \"something\" that makes you wanna know more about 'em. \n", "\n", "Like, imagine this: A character who's always trying to do the right thing, but keeps getting tripped up by their own flaws. They're like, \"I'm gonna fix this, I'm gonna fix this, I'm gonna fix this,\" but it never seems to go their way. And every time they fail, it's like, \"<PERSON><PERSON><PERSON>, you know better than that.\"\n", "\n", "But here's the thing - even though they're making these mistakes, you can't help but root for 'em. You're like, \"Come on, get your act together, fam.\" \n", "\n", "That's a compelling character right there. They're not perfect, but they're trying, and that's what makes you care. You want to see them succeed, even when they're not making the best choices. That's the magic of a compelling character. 🦸‍♂️\n", "\n", "------------------------------\n", "👤 You: Great example! How important is the setting in storytelling?\n", "🤖 AI: ...The setting's like the backdrop for the whole story, ya know? It's not just where the action happens; it's also setting the mood, giving you these vibes. \n", "\n", "Imagine a story where everything's dark and gloomy. You feel like you're in a horror movie, right? But then, you got these bright, happy vibes, and you're like, \"Aight, this is gonna be lit.\"\n", "\n", "The setting's also gonna affect how the characters are acting. If they're in a weird place, they're gonna be acting all wild and crazy. But if they're in a normal place, they're gonna be acting normal. \n", "\n", "So, the setting's like the cherry on top of the ice cream. It's not the main thing, but it's what makes the whole story taste so good. 🍦🍩\n", "\n", "In the end, the setting's gonna make or break the story. If it's good, the story's gonna be straight fire. If it's bad, the story's gonna be a snooze fest. So, make sure you pick your setting right, fam. 🌍💡\n", "\n", "------------------------------\n", "\n", "📊 Conversation Summary: {'total_messages': 6, 'user_messages': 3, 'assistant_messages': 3}\n"]}], "source": ["class StreamingChatInterface:\n", "    def __init__(self, model=\"Helpingai3-raw\"):\n", "        self.hai = HAI()\n", "        self.model = model\n", "        self.conversation = []\n", "        self.system_message = {\n", "            \"role\": \"system\",\n", "            \"content\": \"You are a helpful and friendly AI assistant. Provide engaging and informative responses.\"\n", "        }\n", "    \n", "    def stream_response(self, user_message, show_typing=True):\n", "        \"\"\"Stream a response to user message\"\"\"\n", "        # Add user message to conversation\n", "        self.conversation.append({\"role\": \"user\", \"content\": user_message})\n", "        \n", "        # Prepare messages for API\n", "        messages = [self.system_message] + self.conversation\n", "        \n", "        print(f\"👤 You: {user_message}\")\n", "        print(\"🤖 AI: \", end=\"\")\n", "        \n", "        if show_typing:\n", "            # Simulate typing indicator\n", "            for _ in range(3):\n", "                print(\".\", end=\"\", flush=True)\n", "                time.sleep(0.5)\n", "            print(\"\\r🤖 AI: \", end=\"\")\n", "        \n", "        # Stream the response\n", "        stream = self.hai.chat.completions.create(\n", "            model=self.model,\n", "            messages=messages,\n", "            stream=True,\n", "            temperature=0.7\n", "        )\n", "        \n", "        assistant_response = \"\"\n", "        for chunk in stream:\n", "            if chunk.choices[0].delta.content:\n", "                content = chunk.choices[0].delta.content\n", "                print(content, end=\"\", flush=True)\n", "                assistant_response += content\n", "                time.sleep(0.02)\n", "        \n", "        print(\"\\n\")\n", "        \n", "        # Add assistant response to conversation\n", "        self.conversation.append({\"role\": \"assistant\", \"content\": assistant_response})\n", "        \n", "        return assistant_response\n", "    \n", "    def get_conversation_summary(self):\n", "        \"\"\"Get a summary of the conversation\"\"\"\n", "        total_messages = len(self.conversation)\n", "        user_messages = len([msg for msg in self.conversation if msg[\"role\"] == \"user\"])\n", "        assistant_messages = len([msg for msg in self.conversation if msg[\"role\"] == \"assistant\"])\n", "        \n", "        return {\n", "            \"total_messages\": total_messages,\n", "            \"user_messages\": user_messages,\n", "            \"assistant_messages\": assistant_messages\n", "        }\n", "\n", "# Create streaming chat interface\n", "chat = StreamingChatInterface()\n", "\n", "print(\"💬 Interactive Streaming Chat Demo:\")\n", "print(\"=\" * 50)\n", "\n", "# Simulate a conversation\n", "demo_messages = [\n", "    \"Hello! Can you help me understand what makes a good story?\",\n", "    \"That's helpful! Can you give me an example of a compelling character?\",\n", "    \"Great example! How important is the setting in storytelling?\"\n", "]\n", "\n", "for message in demo_messages:\n", "    chat.stream_response(message)\n", "    print(\"-\" * 30)\n", "\n", "# Show conversation summary\n", "summary = chat.get_conversation_summary()\n", "print(f\"\\n📊 Conversation Summary: {summary}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛡️ Error Handling in Streaming\n", "\n", "Robust error handling for streaming responses."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🛡️ Robust Streaming: Write a haiku about artificial intelligence and cr...\n", "==================================================\n", "Attempt 1: AI's creativity blooms,\n", "Machines dream, humans create,\n", "Together, art thrives.\n", "✅ Streaming completed successfully!\n"]}], "source": ["from HelpingAI import HAIError, RateLimitError, AuthenticationError\n", "\n", "def robust_streaming(prompt, max_retries=3):\n", "    \"\"\"Streaming with comprehensive error handling\"\"\"\n", "    print(f\"🛡️ Robust Streaming: {prompt[:50]}...\")\n", "    print(\"=\" * 50)\n", "    \n", "    for attempt in range(max_retries):\n", "        try:\n", "            print(f\"Attempt {attempt + 1}: \", end=\"\")\n", "            \n", "            stream = hai.chat.completions.create(\n", "                model=\"Helpingai3-raw\",\n", "                messages=[{\"role\": \"user\", \"content\": prompt}],\n", "                stream=True,\n", "                temperature=0.7,\n", "                max_tokens=300\n", "            )\n", "            \n", "            response_parts = []\n", "            \n", "            for chunk in stream:\n", "                try:\n", "                    if chunk.choices[0].delta.content:\n", "                        content = chunk.choices[0].delta.content\n", "                        print(content, end=\"\", flush=True)\n", "                        response_parts.append(content)\n", "                        time.sleep(0.02)\n", "                \n", "                except Exception as chunk_error:\n", "                    print(f\"\\n⚠️ Chunk error: {chunk_error}\")\n", "                    continue\n", "            \n", "            print(\"\\n✅ Streaming completed successfully!\")\n", "            return \"\".join(response_parts)\n", "        \n", "        except RateLimitError:\n", "            print(f\"\\n⏰ Rate limit hit on attempt {attempt + 1}\")\n", "            if attempt < max_retries - 1:\n", "                wait_time = 2 ** attempt  # Exponential backoff\n", "                print(f\"Waiting {wait_time} seconds before retry...\")\n", "                time.sleep(wait_time)\n", "        \n", "        except AuthenticationError:\n", "            print(\"\\n❌ Authentication error - check your API key\")\n", "            break\n", "        \n", "        except HAIError as e:\n", "            print(f\"\\n🚨 API error on attempt {attempt + 1}: {e}\")\n", "            if attempt < max_retries - 1:\n", "                print(\"Retrying...\")\n", "                time.sleep(1)\n", "        \n", "        except Exception as e:\n", "            print(f\"\\n💥 Unexpected error: {e}\")\n", "            break\n", "    \n", "    print(\"❌ All retry attempts failed\")\n", "    return None\n", "\n", "# Test robust streaming\n", "result = robust_streaming(\"Write a haiku about artificial intelligence and creativity.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Streaming Performance Analysis\n", "\n", "Analyze streaming performance and characteristics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔬 Performance Analysis:\n", "============================================================\n", "📊 Analyzing Streaming Performance:\n", "Model: Helpingai3-raw\n", "Prompt: Explain the concept of machine learning in simple ...\n", "==================================================\n", "Response: Ai<PERSON>, listen up. Machine learning? It's like teaching a computer to learn and get smarter on its own. Imagine you're trying to figure out if a picture is a cat or a dog. You'd look at the shape of the ears, the color of the fur, and other stuff. A computer does the same thing with machine learning, but it gets better at it the more it sees.\n", "\n", "So, let's say you want the computer to learn what a cat looks like. You show it a bunch of pictures of cats and tell it \"this is a cat\". The computer looks at the pictures, sees the whiskers, the pointy ears, and all that. It starts to build a \"model\" in its head - like a mental picture of what a cat looks like.\n", "\n", "Now, when you show it a new picture, it can say \"that's a cat\" or \"no, that's not a cat\". The more pictures it sees, the better it gets. It's like the computer is going to school, but instead of a human teacher, it's learning by itself.\n", "\n", "One example is a spam filter for emails. The computer looks at all the emails and learns what words and phrases usually belong to spam. So next time you get an email, the computer can tell if it's spam or not.\n", "\n", "Another example is a recommendation system on a website like Netflix. Based on what you've watched, the computer suggests other movies or shows you might like. It's like it's saying \"you liked this, so you might like this too\".\n", "\n", "So, in simple terms, machine learning is like teaching a computer how to learn by itself, so it can get smarter and make better decisions over time. It's like giving a baby a magnifying glass, and letting it figure out the world on its own. Pretty cool, right?\n", "\n", "📈 Performance Metrics:\n", "------------------------------\n", "Total Duration: 7.79\n", "Time To First Token: 1.06\n", "Total Chunks: 402\n", "Total Characters: 1598\n", "Estimated Tokens: 386\n", "Avg Chunk Interval: 0.017\n", "Characters Per Second: 205.0\n", "Tokens Per Second: 49.5\n", "\n", "============================================================\n", "📊 Analyzing Streaming Performance:\n", "Model: <PERSON><PERSON><PERSON><PERSON>-2.0-preview\n", "Prompt: Explain the concept of machine learning in simple ...\n", "==================================================\n", "Response: <think>\n", "This question asks me to explain machine learning in simple terms with examples. I should break down the core concept first, then provide relatable examples that make it accessible. Machine learning is about algorithms learning patterns from data to make predictions or decisions without being explicitly programmed. I'll use everyday analogies to make this clear.\n", "</think>\n", "\n", "\n", "Machine learning is really just a fancy name for computers learning from experience—much like how you and I learn things in life.\n", "\n", "Think of it this way: instead of telling a computer exactly what to do for every possible situation (which would be impossible for complex tasks), we show it many examples so it can figure out patterns on its own.\n", "\n", "<think>\n", "Let me think of some clear examples that will help illustrate this concept. I should use examples that connect to everyday experiences people have that aren't necessarily technical. I'll include both classification and prediction examples.\n", "</think>\n", "\n", "\n", "Here are some everyday examples:\n", "\n", "1. **Email spam filters**: Your email program learns which emails are spam by analyzing features like specific words, senders, and formatting. Over time, it gets better at putting ads and scam attempts in your spam folder.\n", "\n", "2. **Recommendation systems**: When Netflix suggests shows you might like or when Amazon recommends products, they're using machine learning to analyze what similar users enjoyed and what you've watched/bought.\n", "\n", "3. **Voice assistants**: <PERSON><PERSON>, <PERSON><PERSON>, and Google Assistant understand your voice commands because they've been trained on countless audio examples of different ways people speak.\n", "\n", "4. **Medical diagnosis**: Some systems can analyze medical images to spot signs of diseases like cancer, having"]}], "source": ["import time\n", "from collections import defaultdict\n", "\n", "class StreamingAnalyzer:\n", "    def __init__(self):\n", "        self.reset_metrics()\n", "    \n", "    def reset_metrics(self):\n", "        self.start_time = None\n", "        self.first_token_time = None\n", "        self.chunk_times = []\n", "        self.chunk_sizes = []\n", "        self.total_tokens = 0\n", "        self.total_characters = 0\n", "    \n", "    def start_analysis(self):\n", "        self.reset_metrics()\n", "        self.start_time = time.time()\n", "    \n", "    def process_chunk(self, chunk):\n", "        current_time = time.time()\n", "        \n", "        if chunk.choices[0].delta.content:\n", "            content = chunk.choices[0].delta.content\n", "            \n", "            # Record first token time\n", "            if self.first_token_time is None:\n", "                self.first_token_time = current_time\n", "            \n", "            # Record chunk metrics\n", "            self.chunk_times.append(current_time)\n", "            self.chunk_sizes.append(len(content))\n", "            self.total_characters += len(content)\n", "            \n", "            # Rough token estimation\n", "            self.total_tokens += len(content.split())\n", "            \n", "            return content\n", "        return \"\"\n", "    \n", "    def get_metrics(self):\n", "        if not self.chunk_times:\n", "            return {\"error\": \"No data collected\"}\n", "        \n", "        end_time = self.chunk_times[-1]\n", "        total_duration = end_time - self.start_time\n", "        time_to_first_token = self.first_token_time - self.start_time if self.first_token_time else 0\n", "        \n", "        # Calculate intervals between chunks\n", "        intervals = []\n", "        for i in range(1, len(self.chunk_times)):\n", "            intervals.append(self.chunk_times[i] - self.chunk_times[i-1])\n", "        \n", "        avg_interval = sum(intervals) / len(intervals) if intervals else 0\n", "        \n", "        return {\n", "            \"total_duration\": round(total_duration, 2),\n", "            \"time_to_first_token\": round(time_to_first_token, 2),\n", "            \"total_chunks\": len(self.chunk_times),\n", "            \"total_characters\": self.total_characters,\n", "            \"estimated_tokens\": self.total_tokens,\n", "            \"avg_chunk_interval\": round(avg_interval, 3),\n", "            \"characters_per_second\": round(self.total_characters / total_duration, 1),\n", "            \"tokens_per_second\": round(self.total_tokens / total_duration, 1)\n", "        }\n", "\n", "def analyze_streaming_performance(prompt, model=\"Helpingai3-raw\"):\n", "    \"\"\"Analyze streaming performance metrics\"\"\"\n", "    analyzer = StreamingAnalyzer()\n", "    \n", "    print(f\"📊 Analyzing Streaming Performance:\")\n", "    print(f\"Model: {model}\")\n", "    print(f\"Prompt: {prompt[:50]}...\")\n", "    print(\"=\" * 50)\n", "    \n", "    analyzer.start_analysis()\n", "    \n", "    stream = hai.chat.completions.create(\n", "        model=model,\n", "        messages=[{\"role\": \"user\", \"content\": prompt}],\n", "        stream=True,\n", "        temperature=0.7,\n", "        max_tokens=400\n", "    )\n", "    \n", "    print(\"Response: \", end=\"\")\n", "    full_response = \"\"\n", "    \n", "    for chunk in stream:\n", "        content = analyzer.process_chunk(chunk)\n", "        if content:\n", "            print(content, end=\"\", flush=True)\n", "            full_response += content\n", "    \n", "    print(\"\\n\\n📈 Performance Metrics:\")\n", "    print(\"-\" * 30)\n", "    \n", "    metrics = analyzer.get_metrics()\n", "    for key, value in metrics.items():\n", "        print(f\"{key.replace('_', ' ').title()}: {value}\")\n", "    \n", "    return metrics, full_response\n", "\n", "# Analyze performance for different models\n", "print(\"🔬 Performance Analysis:\")\n", "print(\"=\" * 60)\n", "\n", "test_prompt = \"Explain the concept of machine learning in simple terms with examples.\"\n", "\n", "# Test HelpingAI3-raw\n", "metrics1, response1 = analyze_streaming_performance(test_prompt, \"Helpingai3-raw\")\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "\n", "# Test Dhanishta 2.0\n", "metrics2, response2 = analyze_streaming_performance(test_prompt, \"Dhanishtha-2.0-preview\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Key Insights About Streaming\n", "\n", "From these examples, we can observe important streaming characteristics:\n", "\n", "### ⚡ Performance Benefits\n", "- **Perceived Speed**: Users see content immediately\n", "- **Better UX**: No waiting for complete responses\n", "- **Interactive Feel**: More conversational experience\n", "- **Early Feedback**: Users can interrupt if needed\n", "\n", "### 🛠️ Implementation Considerations\n", "- **Error Handling**: Robust handling of stream interruptions\n", "- **Buffer Management**: Handling partial content gracefully\n", "- **UI Updates**: Real-time interface updates\n", "- **Performance Monitoring**: Track streaming metrics\n", "\n", "### 🎨 Use Cases for Streaming\n", "- **Long-form Content**: Stories, articles, explanations\n", "- **Interactive Chat**: Real-time conversations\n", "- **Live Demonstrations**: Step-by-step tutorials\n", "- **Creative Writing**: Poetry, stories, creative content\n", "\n", "## 🚀 Best Practices\n", "\n", "- **<PERSON><PERSON> Errors Gracefully**: Implement retry logic and fallbacks\n", "- **Show Progress**: Visual indicators for streaming status\n", "- **B<PERSON>er Wisely**: Balance responsiveness with stability\n", "- **Monitor Performance**: Track metrics for optimization\n", "- **User Control**: Allow users to stop/pause streams\n", "\n", "## 📚 Next Steps\n", "\n", "- **[04-parameters.ipynb](04-parameters.ipynb)** - Fine-tuning AI behavior\n", "- **[../advanced/](../advanced/)** - Advanced streaming applications\n", "- **[../../examples/applications/](../../examples/applications/)** - Real-world streaming implementations\n", "\n", "---\n", "\n", "**Create engaging real-time AI experiences with streaming! 🌊✨**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}