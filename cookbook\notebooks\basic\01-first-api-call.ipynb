{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Your First HelpingAI API Call 🚀\n", "\n", "Welcome to HelpingAI! This notebook will guide you through making your first API call and experiencing the power of our AI models, including the revolutionary **Dhanishta 2.0** with intermediate thinking capabilities.\n", "\n", "## What You'll Learn\n", "- How to set up the HelpingAI client\n", "- Make your first API call\n", "- Experience Dhanishta 2.0's thinking process\n", "- Compare different models\n", "- Handle basic errors"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Setup\n", "\n", "First, let's install the HelpingAI SDK and set up our environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install HelpingAI SDK\n", "%pip install HelpingAI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ HelpingAI client initialized successfully!\n"]}], "source": ["# Import required libraries\n", "import os\n", "from HelpingAI import HAI\n", "\n", "# Set your API key (replace with your actual key)\n", "# You can get your API key from: https://helpingai.co/dashboard\n", "os.environ[\"HAI_API_KEY\"] = \"hl-***********************\"\n", "\n", "# Initialize the client\n", "hai = HAI()\n", "\n", "print(\"✅ HelpingAI client initialized successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🤖 Explore Available Models\n", "\n", "Let's see what models are available to us."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🤖 Available HelpingAI Models:\n", "==================================================\n", "📋 Model ID: <PERSON><PERSON><PERSON><PERSON>-2.0-preview\n", "📝 Name: <PERSON><PERSON><PERSON><PERSON>-2.0-preview\n", "------------------------------\n", "📋 Model ID: Helpingai3-raw\n", "📝 Name: Helpingai3-raw\n", "------------------------------\n"]}], "source": ["# List all available models\n", "models = hai.models.list()\n", "\n", "print(\"🤖 Available HelpingAI Models:\")\n", "print(\"=\" * 50)\n", "\n", "for model in models:\n", "    print(f\"📋 Model ID: {model.id}\")\n", "    print(f\"📝 Name: {model.name}\")\n", "    if hasattr(model, 'description') and model.description:\n", "        print(f\"📖 Description: {model.description}\")\n", "    print(\"-\" * 30)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💬 Your First Chat Completion\n", "\n", "Let's start with a simple chat completion using HelpingAI3-raw."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🤖 HelpingAI3-raw Response:\n", "========================================\n", "Yo, emotional intelligence (or EI) is all about being aware of and managing your own emotions, as well as understanding and responding to the emotions of others. It's like having an emotional radar that helps you navigate social situations like a pro.\n", "\n", "Here's the tea: EI has three key parts:\n", "\n", "1. **Self-awareness** - Knowing what you're feeling and how it affects your actions.\n", "2. **Self-management** - Keeping your emotions in check and using them to make good choices.\n", "3. **Social awareness/relationship management** - Understanding others' emotions and knowing how to connect with them.\n", "\n", "Having a high EI score is a game-changer. It helps you communicate better, solve problems, and build strong relationships. It's not just about being smart, it's about being smart about emotions. 🚀\n", "\n", "So, keep that emotional intelligence on lock!\n"]}], "source": ["# Simple chat completion with HelpingAI3-raw\n", "response = hai.chat.completions.create(\n", "    model=\"Helpingai3-raw\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"Hello! Can you tell me about emotional intelligence?\"}\n", "    ]\n", ")\n", "\n", "print(\"🤖 HelpingAI3-raw Response:\")\n", "print(\"=\" * 40)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧠 Experience Dhanishta 2.0's Thinking Process\n", "\n", "Now let's see something truly revolutionary - an AI that shows its thinking process!"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 Dhanishta 2.0 with Thinking Process:\n", "==================================================\n", "<think>\n", "I need to count the letter 'r' in the word \\\"strawberry\\\". Let me spell it out: s-t-r-a-w-b-e-r-r-y. Now I'll scan for the letter 'r'. The third letter is 'r', then after 'b-e' comes another 'r', and another 'r' before 'y'. So that's three 'r' letters in total. Let me double-check: s-t-r-a-w-b-e-r-r-y. Yes, three 'r's.\n", "</think>\n", "\n", "Looking at the word \\\"strawberry,\\\" I can see that there are three instances of the letter 'r'. \n", "\n", "<think>\n", "Let me break down the word to make sure I'm counting correctly: s-t-r-a-w-b-e-r-r-y. The 'r' appears in the 3rd position (straw...), then again after 'b-e' (strawb...err...), and a third time right before 'y' (strawb...err...y). So that's definitely three 'r' letters.\n", "</think>\n", "\n", "The word \\\"strawberry\\\" contains three 'r' letters. I can see them in the 3rd position, then two more consecutive 'r's near the end of the word. It's one of those words where the same letter appears multiple times in a row, which can make counting a bit tricky!\n"]}], "source": ["# Experience intermediate thinking with <PERSON><PERSON><PERSON><PERSON> 2.0\n", "response = hai.chat.completions.create(\n", "    model=\"Dhanishtha-2.0-preview\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"How many letter 'r' are in the word 'strawberry'?\"}\n", "    ],\n", "    hide_think=False  # Show the thinking process!\n", ")\n", "\n", "print(\"🧠 Dhanishta 2.0 with Thinking Process:\")\n", "print(\"=\" * 50)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 Compare: With and Without Thinking\n", "\n", "Let's see the difference when we hide the thinking process."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 Dhanishta 2.0 (Clean Output):\n", "========================================\n", "To find how many letter 'r' are in \\\"strawberry,\\\" I'll need to go through the word letter by letter.\n", "\n", "Looking at the word \\\"strawberry,\\\" I can see that the letter 'r' appears three times. \n", "\n", "The 'r' is in position 3 (the third letter), then again at positions 8 and 9. When we count all instances of 'r' in the word, we get a total of 3 letters.\n", "\n", "So there are 3 letter 'r's in the word \\\"strawberry.\\\n"]}], "source": ["# Same question but hide the thinking process\n", "response_clean = hai.chat.completions.create(\n", "    model=\"Dhanishtha-2.0-preview\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"How many letter 'r' are in the word 'strawberry'?\"}\n", "    ],\n", "    hide_think=True  # Hide the thinking process\n", ")\n", "\n", "print(\"🧠 Dhanishta 2.0 (Clean Output):\")\n", "print(\"=\" * 40)\n", "print(response_clean.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧮 Complex Reasoning Example\n", "\n", "Let's give Dhanishta 2.0 a more challenging problem to see its reasoning in action."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧮 Complex Reasoning with <PERSON><PERSON><PERSON><PERSON> 2.0:\n", "==================================================\n", "<think>\n", "This is a classic rate problem that can be tricky. Let me work through it step by step. We have 5 machines making 5 widgets in 5 minutes. I need to find how long 100 machines would take to make 100 widgets.\n", "\n", "First, let's find the production rate. 5 machines make 5 widgets in 5 minutes. That means each machine makes 1 widget in 5 minutes, or the rate is 1/5 widgets per minute per machine.\n", "\n", "Now with 100 machines, the total production rate would be 100 × (1/5) = 20 widgets per minute. To make 100 widgets at 20 widgets per minute, it would take 100 ÷ 20 = 5 minutes.\n", "</think>\n", "\n", "If it takes 5 machines 5 minutes to make 5 widgets, then 100 machines would take 5 minutes to make 100 widgets.\n", "\n", "<think>\n", "Let me double-check this with a different approach. The key insight is that we're maintaining the same production rate per machine. Each machine still takes 5 minutes to make 1 widget, regardless of how many machines we have. So if we scale up both the number of machines and the number of widgets proportionally, the time remains the same.\n", "</think>\n", "\n", "The answer is 5 minutes. \n", "\n", "When we have 5 machines making 5 widgets in 5 minutes, that means each machine produces 1 widget in 5 minutes. This gives us a rate of 1/5 widgets per minute per machine.\n", "\n", "With 100 machines working at the same rate, each machine still takes 5 minutes to make 1 widget. So all 100 machines would produce 100 widgets in exactly 5 minutes.\n", "\n", "This is a great example of how scaling up both the resources (machines) and the goal (widgets) proportionally doesn't change the time required when the production rate per unit remains constant.\n"]}], "source": ["# Complex reasoning problem\n", "response = hai.chat.completions.create(\n", "    model=\"Dhanishtha-2.0-preview\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\", \n", "            \"content\": \"If it takes 5 machines 5 minutes to make 5 widgets, how long would it take 100 machines to make 100 widgets?\"\n", "        }\n", "    ],\n", "    hide_think=False,\n", "    temperature=0.3  # Lower temperature for logical reasoning\n", ")\n", "\n", "print(\"🧮 Complex Reasoning with <PERSON><PERSON><PERSON><PERSON> 2.0:\")\n", "print(\"=\" * 50)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💝 Emotional Intelligence with HelpingAI3\n", "\n", "Now let's see HelpingAI3-raw's emotional intelligence in action."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💝 HelpingAI3-raw Emotional Response:\n", "=============================================\n", "Aight, first off, you ain't alone, fam. Dealing with work stress is a real struggle. Here's the tea:\n", "\n", "1. 🚫 Stop the Overthinking: Your mind is running wild, but that's just your brain being a worry-wart. It's not the end of the world if things don't go 100%. You got this, fam.\n", "\n", "2. 🌈 Take Breaks: You ain't a robot, you need to chill. Take a few mins to relax, breathe, and clear your mind. It'll help you come back stronger.\n", "\n", "3. 📝 Write It Down: If your mind is racing, get it out of your head and onto paper. Write down your worries, then rip 'em up. It feels good, trust me.\n", "\n", "4. 💪 Find Your Tribe: Talk to your homies, see how they handle stress. They might have some fire advice to share.\n", "\n", "5. 🧘‍♂️ Meditate: Just 10 mins of zen can make a world of difference. Calm your mind, and you'll be back to crushing it in no time.\n", "\n", "6. 🏃‍♂️ Exercise: Get your blood pumping and your endorphins going. It'll keep you balanced and ready to handle whatever comes your way.\n", "\n", "7. 🎤 Positive Vibes: Ditch the negative thoughts and replace them with some positive energy. Remind yourself why you're doing this and how far you've come.\n", "\n", "Remember, you got this, fam. Just take it one step at a time and don't be shy to ask for help when you need it. You're tougher than you think. 💪😎\n"]}], "source": ["# Emotional intelligence example\n", "response = hai.chat.completions.create(\n", "    model=\"Helpingai3-raw\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\", \n", "            \"content\": \"I'm feeling overwhelmed with work and don't know how to cope. Can you help?\"\n", "        }\n", "    ],\n", "    temperature=0.8  # Higher temperature for more empathetic responses\n", ")\n", "\n", "print(\"💝 HelpingAI3-raw Emotional Response:\")\n", "print(\"=\" * 45)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Response Information\n", "\n", "Let's examine the response object to understand what information we get back."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Response Object Information:\n", "========================================\n", "Model used: HelpingAI/Dhanishtha-2.0-preview\n", "Number of choices: 1\n", "Message role: assistant\n", "Finish reason: stop\n", "\n", "📈 Token Usage:\n", "Prompt tokens: 13\n", "Completion tokens: 333\n", "Total tokens: 346\n", "\n", "💬 Response:\n", "<think>\n", "This question is asking for a definition of artificial intelligence. I should provide a clear, thoughtful explanation that captures both the technical aspects and the broader implications. Sin...\n"]}], "source": ["# Get detailed response information\n", "response = hai.chat.completions.create(\n", "    model=\"Dhanishtha-2.0-preview\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"What is artificial intelligence?\"}\n", "    ]\n", ")\n", "\n", "print(\"📊 Response Object Information:\")\n", "print(\"=\" * 40)\n", "print(f\"Model used: {response.model if hasattr(response, 'model') else 'N/A'}\")\n", "print(f\"Number of choices: {len(response.choices)}\")\n", "print(f\"Message role: {response.choices[0].message.role}\")\n", "print(f\"Finish reason: {response.choices[0].finish_reason}\")\n", "\n", "if hasattr(response, 'usage') and response.usage:\n", "    print(f\"\\n📈 Token Usage:\")\n", "    print(f\"Prompt tokens: {response.usage.prompt_tokens}\")\n", "    print(f\"Completion tokens: {response.usage.completion_tokens}\")\n", "    print(f\"Total tokens: {response.usage.total_tokens}\")\n", "\n", "print(f\"\\n💬 Response:\")\n", "print(response.choices[0].message.content[:200] + \"...\" if len(response.choices[0].message.content) > 200 else response.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ Error Handling\n", "\n", "Let's learn how to handle errors gracefully."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🛡️ Safe API Call Result:\n", "<think>\n", "The user has sent \\\"Hello, world!\\\" and is asking me to respond with a detailed, thoughtful, and emotionally intelligent answer. They've also provided specific formatting instructions about using think blocks and ser blocks. This is essentially a test to see how I respond to a simple greeting while demonstrating my reasoning process. I should respond warmly but naturally, without being overly enthusiastic or using excessive slang.\n", "</think>\n", "\n", "Hello! It's nice to connect with you today. I notice you've sent a simple \\\"Hello, world!\\\" which is often used as a starting point in programming and as a greeting.\n", "\n", "<think>\n", "I should elaborate a bit more on what would be a natural response to a simple greeting. I want to be warm but not overly casual, thoughtful without being verbose, and demonstrate the kind of reasoning I'd apply to more complex questions later.\n", "</think>\n", "\n", "I'm here to have a thoughtful conversation with you. Whether you have questions, need help with something specific, or just want to chat—I'm ready to listen and provide a thoughtful response. \n", "\n", "What would you like to talk about today? I'm here for whatever's on your mind.\n"]}], "source": ["from HelpingAI import HAIError, AuthenticationError, RateLimitError\n", "\n", "def safe_api_call(model, message):\n", "    \"\"\"Make an API call with proper error handling\"\"\"\n", "    try:\n", "        response = hai.chat.completions.create(\n", "            model=model,\n", "            messages=[{\"role\": \"user\", \"content\": message}]\n", "        )\n", "        return response.choices[0].message.content\n", "    \n", "    except AuthenticationError:\n", "        return \"❌ Authentication failed. Please check your API key.\"\n", "    \n", "    except RateLimitError:\n", "        return \"⏰ Rate limit exceeded. Please wait before making more requests.\"\n", "    \n", "    except HAIError as e:\n", "        return f\"🚨 API Error: {str(e)}\"\n", "    \n", "    except Exception as e:\n", "        return f\"💥 Unexpected error: {str(e)}\"\n", "\n", "# Test the error handling\n", "result = safe_api_call(\"Dhanishtha-2.0-preview\", \"Hello, world!\")\n", "print(\"🛡️ Safe API Call Result:\")\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Key Takeaways\n", "\n", "Congratulations! You've successfully:\n", "\n", "✅ **Set up the HelpingAI client**  \n", "✅ **Made your first API calls**  \n", "✅ **Experienced <PERSON><PERSON><PERSON><PERSON> 2.0's thinking process**  \n", "✅ **Compared different models**  \n", "✅ **Learned basic error handling**  \n", "\n", "### What Makes HelpingAI Special?\n", "\n", "🧠 **Dhanish<PERSON> 2.0**: World's first AI with intermediate thinking  \n", "💝 **HelpingAI3-raw**: Advanced emotional intelligence  \n", "🌍 **Multilingual**: Support for 39+ languages  \n", "🔄 **Self-Correction**: AI that can fix its own mistakes  \n", "\n", "## 📚 Next Steps\n", "\n", "Ready to explore more? Check out these notebooks:\n", "\n", "- **[02-chat-completions.ipynb](02-chat-completions.ipynb)** - Advanced chat patterns\n", "- **[03-streaming.ipynb](03-streaming.ipynb)** - Real-time streaming responses\n", "- **[04-parameters.ipynb](04-parameters.ipynb)** - Fine-tuning AI behavior\n", "- **[../dhanishta-2.0/](../dhanishta-2.0/)** - Deep dive into intermediate thinking\n", "\n", "---\n", "\n", "**Happy coding with HelpingAI! 🚀✨**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}