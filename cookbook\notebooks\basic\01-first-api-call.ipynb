{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Your First HelpingAI API Call 🚀\n", "\n", "Welcome to HelpingAI! This notebook will guide you through making your first API call and experiencing the power of our AI models, including the revolutionary **Dhanishta 2.0** with intermediate thinking capabilities.\n", "\n", "## What You'll Learn\n", "- How to set up the HelpingAI client\n", "- Make your first API call\n", "- Experience Dhanishta 2.0's thinking process\n", "- Compare different models\n", "- Handle basic errors"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Setup\n", "\n", "First, let's install the HelpingAI SDK and set up our environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install HelpingAI SDK\n", "%pip install HelpingAI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import os\n", "from HelpingAI import HAI\n", "\n", "# Set your API key (replace with your actual key)\n", "# You can get your API key from: https://helpingai.co/dashboard\n", "os.environ[\"HAI_API_KEY\"] = \"your-api-key-here\"\n", "\n", "# Initialize the client\n", "hai = HAI()\n", "\n", "print(\"✅ HelpingAI client initialized successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🤖 Explore Available Models\n", "\n", "Let's see what models are available to us."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# List all available models\n", "models = hai.models.list()\n", "\n", "print(\"🤖 Available HelpingAI Models:\")\n", "print(\"=\" * 50)\n", "\n", "for model in models:\n", "    print(f\"📋 Model ID: {model.id}\")\n", "    print(f\"📝 Name: {model.name}\")\n", "    if hasattr(model, 'description') and model.description:\n", "        print(f\"📖 Description: {model.description}\")\n", "    print(\"-\" * 30)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💬 Your First Chat Completion\n", "\n", "Let's start with a simple chat completion using HelpingAI3-raw."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Simple chat completion with HelpingAI3-raw\n", "response = hai.chat.completions.create(\n", "    model=\"Helpingai3-raw\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"Hello! Can you tell me about emotional intelligence?\"}\n", "    ]\n", ")\n", "\n", "print(\"🤖 HelpingAI3-raw Response:\")\n", "print(\"=\" * 40)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧠 Experience Dhanishta 2.0's Thinking Process\n", "\n", "Now let's see something truly revolutionary - an AI that shows its thinking process!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Experience intermediate thinking with <PERSON><PERSON><PERSON><PERSON> 2.0\n", "response = hai.chat.completions.create(\n", "    model=\"Dhanishtha-2.0-preview\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"How many letter 'r' are in the word 'strawberry'?\"}\n", "    ],\n", "    hide_think=False  # Show the thinking process!\n", ")\n", "\n", "print(\"🧠 Dhanishta 2.0 with Thinking Process:\")\n", "print(\"=\" * 50)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 Compare: With and Without Thinking\n", "\n", "Let's see the difference when we hide the thinking process."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Same question but hide the thinking process\n", "response_clean = hai.chat.completions.create(\n", "    model=\"Dhanishtha-2.0-preview\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"How many letter 'r' are in the word 'strawberry'?\"}\n", "    ],\n", "    hide_think=True  # Hide the thinking process\n", ")\n", "\n", "print(\"🧠 Dhanishta 2.0 (Clean Output):\")\n", "print(\"=\" * 40)\n", "print(response_clean.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧮 Complex Reasoning Example\n", "\n", "Let's give Dhanishta 2.0 a more challenging problem to see its reasoning in action."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Complex reasoning problem\n", "response = hai.chat.completions.create(\n", "    model=\"Dhanishtha-2.0-preview\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\", \n", "            \"content\": \"If it takes 5 machines 5 minutes to make 5 widgets, how long would it take 100 machines to make 100 widgets?\"\n", "        }\n", "    ],\n", "    hide_think=False,\n", "    temperature=0.3  # Lower temperature for logical reasoning\n", ")\n", "\n", "print(\"🧮 Complex Reasoning with <PERSON><PERSON><PERSON><PERSON> 2.0:\")\n", "print(\"=\" * 50)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💝 Emotional Intelligence with HelpingAI3\n", "\n", "Now let's see HelpingAI3-raw's emotional intelligence in action."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Emotional intelligence example\n", "response = hai.chat.completions.create(\n", "    model=\"Helpingai3-raw\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\", \n", "            \"content\": \"I'm feeling overwhelmed with work and don't know how to cope. Can you help?\"\n", "        }\n", "    ],\n", "    temperature=0.8  # Higher temperature for more empathetic responses\n", ")\n", "\n", "print(\"💝 HelpingAI3-raw Emotional Response:\")\n", "print(\"=\" * 45)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Response Information\n", "\n", "Let's examine the response object to understand what information we get back."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get detailed response information\n", "response = hai.chat.completions.create(\n", "    model=\"Dhanishtha-2.0-preview\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"What is artificial intelligence?\"}\n", "    ]\n", ")\n", "\n", "print(\"📊 Response Object Information:\")\n", "print(\"=\" * 40)\n", "print(f\"Model used: {response.model if hasattr(response, 'model') else 'N/A'}\")\n", "print(f\"Number of choices: {len(response.choices)}\")\n", "print(f\"Message role: {response.choices[0].message.role}\")\n", "print(f\"Finish reason: {response.choices[0].finish_reason}\")\n", "\n", "if hasattr(response, 'usage') and response.usage:\n", "    print(f\"\\n📈 Token Usage:\")\n", "    print(f\"Prompt tokens: {response.usage.prompt_tokens}\")\n", "    print(f\"Completion tokens: {response.usage.completion_tokens}\")\n", "    print(f\"Total tokens: {response.usage.total_tokens}\")\n", "\n", "print(f\"\\n💬 Response:\")\n", "print(response.choices[0].message.content[:200] + \"...\" if len(response.choices[0].message.content) > 200 else response.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ Error Handling\n", "\n", "Let's learn how to handle errors gracefully."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from HelpingAI import HAIError, AuthenticationError, RateLimitError\n", "\n", "def safe_api_call(model, message):\n", "    \"\"\"Make an API call with proper error handling\"\"\"\n", "    try:\n", "        response = hai.chat.completions.create(\n", "            model=model,\n", "            messages=[{\"role\": \"user\", \"content\": message}]\n", "        )\n", "        return response.choices[0].message.content\n", "    \n", "    except AuthenticationError:\n", "        return \"❌ Authentication failed. Please check your API key.\"\n", "    \n", "    except RateLimitError:\n", "        return \"⏰ Rate limit exceeded. Please wait before making more requests.\"\n", "    \n", "    except HAIError as e:\n", "        return f\"🚨 API Error: {str(e)}\"\n", "    \n", "    except Exception as e:\n", "        return f\"💥 Unexpected error: {str(e)}\"\n", "\n", "# Test the error handling\n", "result = safe_api_call(\"Dhanishtha-2.0-preview\", \"Hello, world!\")\n", "print(\"🛡️ Safe API Call Result:\")\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Key Takeaways\n", "\n", "Congratulations! You've successfully:\n", "\n", "✅ **Set up the HelpingAI client**  \n", "✅ **Made your first API calls**  \n", "✅ **Experienced <PERSON><PERSON><PERSON><PERSON> 2.0's thinking process**  \n", "✅ **Compared different models**  \n", "✅ **Learned basic error handling**  \n", "\n", "### What Makes HelpingAI Special?\n", "\n", "🧠 **Dhanish<PERSON> 2.0**: World's first AI with intermediate thinking  \n", "💝 **HelpingAI3-raw**: Advanced emotional intelligence  \n", "🌍 **Multilingual**: Support for 39+ languages  \n", "🔄 **Self-Correction**: AI that can fix its own mistakes  \n", "\n", "## 📚 Next Steps\n", "\n", "Ready to explore more? Check out these notebooks:\n", "\n", "- **[02-chat-completions.ipynb](02-chat-completions.ipynb)** - Advanced chat patterns\n", "- **[03-streaming.ipynb](03-streaming.ipynb)** - Real-time streaming responses\n", "- **[04-parameters.ipynb](04-parameters.ipynb)** - Fine-tuning AI behavior\n", "- **[../dhanishta-2.0/](../dhanishta-2.0/)** - Deep dive into intermediate thinking\n", "\n", "---\n", "\n", "**Happy coding with HelpingAI! 🚀✨**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}